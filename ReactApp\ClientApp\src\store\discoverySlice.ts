import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "./store";

import type { WEBEndpointType } from "@iris/discovery.fe.client";
interface DiscoveryState {
  baseUrls: WEBEndpointType[];
  status: "idle" | "loading" | "success" | "error";
}

const initialState: DiscoveryState = {
  baseUrls: [],
  status: "idle",
};

const discoverySlice = createSlice({
  name: "discovery",
  initialState,
  reducers: {
    setBaseUrls: (state, action: PayloadAction<WEBEndpointType[]>) => {
      state.baseUrls = action.payload;
      state.status = "success";
    },
    setDiscoveryLoading: (state) => {
      state.status = "loading";
    },
    setDiscoveryError: (state) => {
      state.status = "error";
    },
    clearBaseUrls: (state) => {
      state.baseUrls = [];
      state.status = "idle";
    },
  },
});

export const {
  setBaseUrls,
  clearBaseUrls,
  setDiscoveryLoading,
  setDiscoveryError,
} = discoverySlice.actions;

export const selectBaseUrls = (state: RootState) => state.discovery.baseUrls;
export const selectDiscoveryStatus = (state: RootState) =>
  state.discovery.status;

export default discoverySlice.reducer;
