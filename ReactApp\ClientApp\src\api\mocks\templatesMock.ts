import type { TemplateDetailResponse } from "@/types/templates";

export const mockTemplateColumns = [
  {
    key: "templateName",
    displayValue: "Template Name",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "createdBy",
    displayValue: "Created By",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "createdOn",
    displayValue: "Created On",
    dataType: "date",
    defaultVisible: true,
    sortable: true,
    filterType: "range",
  },
  {
    key: "description",
    displayValue: "Description",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "status",
    displayValue: "Status",
    dataType: "text",
    defaultVisible: false,
    sortable: true,
    filterType: "multiSelect",
    filterData: [
      {
        name: "Active",
        value: "Active",
      },
      {
        name: "Inactive",
        value: "Inactive",
      },
    ],
  },
  {
    key: "assignedClients",
    displayValue: "Assigned Clients",
    dataType: "text",
    defaultVisible: false,
    sortable: true,
    filterType: "none",
  },
];

export const mockTemplatesData = {
  records: [
    {
      id: "P-000000052",
      templateName: "Project Kickoff Email",
      createdBy: "Alice Smith",
      createdOn: "2023-01-15T10:00:00.000Z", // Correct
      description:
        "Standard email template for initiating new client projects, covering initial steps and expectations.",
      status: "Active",
      assignedClients: ["Client A", "Client B", "Client C"],
    },
    {
      id: "P-000000040",
      templateName: "Weekly Progress Report",
      createdBy: "Bob Johnson",
      createdOn: "2023-02-01T14:30:00.000Z", // Corrected: colon changed to period
      description:
        "Template for weekly updates on project progress, milestones, and upcoming tasks.",
      status: "Active",
      assignedClients: ["Client A", "Client D", "Client E"],
    },
    {
      id: "3",
      templateName: "Client Onboarding Checklist",
      createdBy: "Charlie Brown",
      createdOn: "2023-02-20T09:15:00.000Z", // Corrected: colon changed to period
      description:
        "Comprehensive checklist to ensure all steps are followed when bringing on a new client.",
      status: "Under Review",
      assignedClients: ["Client E", "Client F"],
    },
    {
      id: "4",
      templateName: "Service Agreement Draft (Standard)",
      createdBy: "Diana Prince",
      createdOn: "2023-03-10T11:45:00.000Z", // Corrected: colon changed to period
      description:
        "Legal template for drafting standard service agreements, including general terms.",
      status: "Inactive",
      assignedClients: [],
    },
    {
      id: "5",
      templateName: "Marketing Campaign Brief - Q2",
      createdBy: "Eve Adams",
      createdOn: "2023-03-25T16:00:00.000Z", // Corrected: colon changed to period
      description:
        "Template for outlining Q2 marketing campaigns, objectives, and target audience segmentation.",
      status: "Active",
      assignedClients: ["Client B", "Client C", "Client F"],
    },
    {
      id: "6",
      templateName: "Post-Project Feedback Survey",
      createdBy: "Frank White",
      createdOn: "2023-04-05T08:00:00.000Z", // Corrected: colon changed to period
      description:
        "Customer satisfaction survey template for gathering insights after project completion.",
      status: "Active",
      assignedClients: ["Client D", "Client G", "Client H"],
    },
    {
      id: "7",
      templateName: "Annual Budget Proposal",
      createdBy: "Grace Taylor",
      createdOn: "2023-04-18T13:00:00.000Z", // Corrected: colon changed to period
      description:
        "Template for creating detailed annual budget proposals for large-scale projects.",
      status: "Under Review",
      assignedClients: ["Client E", "Client H"],
    },
    {
      id: "8",
      templateName: "Internal Team Meeting Agenda (Dev)",
      createdBy: "Henry Green",
      createdOn: "2023-05-01T09:00:00.000Z", // Corrected: colon changed to period
      description:
        "Standard agenda for weekly internal development team meetings, focusing on sprints.",
      status: "Active",
      assignedClients: [],
    },
    {
      id: "9",
      templateName: "Software Bug Report Form (Detailed)",
      createdBy: "Ivy Black",
      createdOn: "2023-05-15T10:30:00.000Z", // Corrected: colon changed to period
      description:
        "Template for reporting software bugs with detailed steps to reproduce and expected behavior.",
      status: "Active",
      assignedClients: ["Client A", "Client F", "Client I"],
    },
    {
      id: "10",
      templateName: "Holiday Greetings Email (2023)",
      createdBy: "Jack King",
      createdOn: "2023-06-01T11:00:00.000Z", // Corrected: colon changed to period
      description:
        "General holiday greetings email for all clients, customized for 2023.",
      status: "Archived",
      assignedClients: [
        "Client A",
        "Client B",
        "Client C",
        "Client D",
        "Client E",
        "Client F",
        "Client G",
        "Client H",
        "Client I",
        "Client J",
      ],
    },
    {
      id: "11",
      templateName: "New Feature Request Form",
      createdBy: "Kelly Lewis",
      createdOn: "2023-06-10T14:00:00.000Z", // Corrected: colon changed to period
      description:
        "Form for clients to submit new feature requests, including priority and justification.",
      status: "Active",
      assignedClients: ["Client A", "Client D", "Client I"],
    },
    {
      id: "12",
      templateName: "Client Performance Review",
      createdBy: "Liam Clark",
      createdOn: "2023-06-25T09:30:00.000Z", // Corrected: colon changed to period
      description:
        "Template for conducting quarterly performance reviews with key clients.",
      status: "Active",
      assignedClients: ["Client B", "Client E", "Client G"],
    },
    {
      id: "13",
      templateName: "Social Media Content Plan",
      createdBy: "Mia Hall",
      createdOn: "2023-07-01T15:45:00.000Z", // Corrected: colon changed to period
      description:
        "Monthly plan for social media content across various platforms, including post ideas and schedule.",
      status: "Active",
      assignedClients: ["Client C", "Client F"],
    },
    {
      id: "14",
      templateName: "Risk Assessment Matrix",
      createdBy: "Noah Young",
      createdOn: "2023-07-15T10:00:00.000Z", // Corrected: colon changed to period
      description:
        "Template for identifying, assessing, and mitigating project risks.",
      status: "Under Review",
      assignedClients: ["Client H", "Client J"],
    },
    {
      id: "15",
      templateName: "NDA Document Template",
      createdBy: "Olivia Scott",
      createdOn: "2023-07-20T12:00:00.000Z", // Corrected: colon changed to period
      description:
        "Non-Disclosure Agreement template for protecting sensitive information.",
      status: "Inactive",
      assignedClients: [],
    },
    {
      id: "16",
      templateName: "Customer Support FAQ",
      createdBy: "Peter Adams",
      createdOn: "2023-08-01T09:00:00.000Z", // Corrected: colon changed to period
      description:
        "Template for compiling frequently asked questions and their answers for customer support.",
      status: "Active",
      assignedClients: ["Client G", "Client I"],
    },
    {
      id: "17",
      templateName: "Project Closure Report",
      createdBy: "Quinn Baker",
      createdOn: "2023-08-10T16:00:00.000Z", // Corrected: colon changed to period
      description:
        "Final report template for project closure, summarizing outcomes and lessons learned.",
      status: "Active",
      assignedClients: ["Client A", "Client B"],
    },
    {
      id: "18",
      templateName: "Invoice Template (Standard)",
      createdBy: "Rachel Carter",
      createdOn: "2023-08-20T11:00:00.000Z", // Corrected: colon changed to period
      description:
        "Standard invoice template for billing clients for services rendered.",
      status: "Active",
      assignedClients: [
        "Client A",
        "Client B",
        "Client C",
        "Client D",
        "Client E",
        "Client F",
        "Client G",
        "Client H",
        "Client I",
        "Client J",
      ],
    },
    {
      id: "19",
      templateName: "Employee Onboarding Pack",
      createdBy: "Sam Davis",
      createdOn: "2023-09-01T09:00:00.000Z", // Corrected: colon changed to period
      description:
        "Information packet for new employees, covering company policies and benefits.",
      status: "Active",
      assignedClients: [], // Internal template
    },
    {
      id: "20",
      templateName: "Press Release Template",
      createdBy: "Tina Evans",
      createdOn: "2023-09-15T13:00:00.000Z", // Corrected: colon changed to period
      description:
        "Template for drafting official press releases for company news.",
      status: "Under Review",
      assignedClients: ["Client F"],
    },
    {
      id: "21",
      templateName: "Website Content Audit Checklist",
      createdBy: "Uma Ford",
      createdOn: "2023-09-20T10:00:00.000Z", // Corrected: colon changed to period
      description:
        "Checklist for auditing website content for SEO, accuracy, and user experience.",
      status: "Active",
      assignedClients: ["Client J"],
    },
    {
      id: "22",
      templateName: "Sales Lead Qualification Form",
      createdBy: "Victor Gomez",
      createdOn: "2023-10-01T14:00:00.000Z", // Corrected: colon changed to period
      description:
        "Form to qualify potential sales leads based on predefined criteria.",
      status: "Active",
      assignedClients: [], // Internal sales template
    },
    {
      id: "23",
      templateName: "Client Testimonial Request",
      createdBy: "Wendy Hill",
      createdOn: "2023-10-10T11:00:00.000Z", // Corrected: colon changed to period
      description:
        "Email template for requesting testimonials from satisfied clients.",
      status: "Active",
      assignedClients: ["Client A", "Client C", "Client H"],
    },
    {
      id: "24",
      templateName: "Software Update Release Notes",
      createdBy: "Xavier Kim",
      createdOn: "2023-10-25T16:00:00.000Z", // Corrected: colon changed to period
      description:
        "Template for detailing changes and new features in software updates.",
      status: "Active",
      assignedClients: ["Client A", "Client D", "Client I"],
    },
    {
      id: "25",
      templateName: "Emergency Contact Form",
      createdBy: "Yara Lopez",
      createdOn: "2023-11-01T09:00:00.000Z", // Corrected: colon changed to period
      description:
        "Form for collecting emergency contact information for employees and contractors.",
      status: "Active",
      assignedClients: [], // Internal HR template
    },
  ],
  pageCount: 2,
  pageNuer: 1,
  pageSize: 20,
  totalRecordCount: 25,
};

// Mock data for template details by ID
export const mockTemplateDetails: Record<string, TemplateDetailResponse> = {
  "P-000000052": {
    templateName: "Project Kickoff Email",
    templateDescription:
      "Standard email template for initiating new client projects, covering initial steps and expectations.",
    active: true,
    nodes: [
      {
        id: 1,
        name: "Primary Folder 1",
        childNodes: [
          { id: 2, name: "Secondary Folder 1" },
          { id: 3, name: "Secondary Folder 2" },
        ],
      },
      {
        id: 4,
        name: "Primary Folder 2",
        childNodes: [{ id: 5, name: "Secondary Folder 3" }],
      },
    ],
  },
  "P-000000040": {
    templateName: "Weekly Progress Report",
    templateDescription:
      "Template for weekly updates on project progress, milestones, and upcoming tasks.",
    active: true,
    nodes: [
      {
        id: 6,
        name: "Reports",
        childNodes: [
          { id: 7, name: "Weekly Reports" },
          { id: 8, name: "Monthly Reports" },
          { id: 9, name: "Quarterly Reports" },
        ],
      },
      {
        id: 10,
        name: "Templates",
        childNodes: [
          { id: 11, name: "Standard Templates" },
          { id: 12, name: "Custom Templates" },
        ],
      },
    ],
  },
  "3": {
    templateName: "Client Onboarding Checklist",
    templateDescription:
      "Comprehensive checklist to ensure all steps are followed when bringing on a new client.",
    active: false,
    nodes: [
      {
        id: 13,
        name: "Onboarding Steps",
        childNodes: [
          { id: 14, name: "Initial Contact" },
          { id: 15, name: "Documentation" },
          { id: 16, name: "Setup" },
          { id: 17, name: "Training" },
        ],
      },
    ],
  },
};
