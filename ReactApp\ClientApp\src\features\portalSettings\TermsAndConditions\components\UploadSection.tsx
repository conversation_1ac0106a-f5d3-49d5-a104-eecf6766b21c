import { useRef, useState, useEffect } from "react";
import { Upload } from "@progress/kendo-react-upload";
import { Button } from "@progress/kendo-react-buttons";
import { Input } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";
import classNames from "classnames";
import logger from "@/utils/logger";
import {
  ALLOWED_FILE_EXTENSIONS,
  MAX_UPLOAD_FILE_SIZE_BYTES,
} from "../constants";

interface Props {
  uploadedFileName: string;
  onUpload: (_e: any) => void;
  onRemove: () => void;
  fileNameFromTerms?: string;
}

export default function UploadSection({
  uploadedFileName,
  onUpload,
  onRemove,
  fileNameFromTerms,
}: Props) {
  const { t } = useTranslation("dashboard");
  const [uploadError, setUploadError] = useState("");
  const [uploadFiles, setUploadFiles] = useState<any[]>([]);
  const uploadRef = useRef<any>(null);

  const fileNameToShow = uploadedFileName || fileNameFromTerms || "";

  // Reset files and error when uploadedFileName is cleared
  useEffect(() => {
    if (!uploadedFileName) {
      setUploadFiles([]);
      setUploadError("");
    }
  }, [uploadedFileName]);

  const handleAdd = (event: any) => {
    const file = event.affectedFiles?.[0];
    const errors = file?.validationErrors;

    logger.debug("File added", {
      name: file?.name,
      errors,
    });

    onUpload(event);
    setUploadFiles(event.newState);

    if (errors?.length) {
      if (errors.includes("invalidFileExtension")) {
        setUploadError(t("unsupported_format"));
      } else if (errors.includes("invalidMaxFileSize")) {
        setUploadError(t("file_size_exceeded"));
      } else {
        setUploadError(t("unknown_error"));
      }
      return;
    }

    setUploadError("");
    uploadRef.current?.saveFiles?.(event.affectedFiles);
  };

  const handleBeforeUpload = async (event: any) => {
    await new Promise((resolve) => setTimeout(resolve, 100));
    event.preventDefault(); // block real upload
  };

  return (
    <div className="upload-section">
      <label className="upload-label">{t("upload_label")}</label>

      <div className="upload-input-row">
        <Input
          value={fileNameToShow}
          placeholder={t("upload_placeholder")}
          readOnly
          valid={!uploadError}
          className={classNames("upload-input", { "k-invalid": uploadError })}
          aria-describedby={uploadError ? "upload-error-msg" : undefined}
        />
        {uploadedFileName && (
          <Button icon="close" fillMode="flat" onClick={onRemove} />
        )}
      </div>

      {uploadError && (
        <div
          id="upload-error-msg"
          className="upload-error-label k-text-error k-display-flex k-align-items-center k-gap-1"
          role="alert"
        >
          {/* <span className="k-icon k-i-warning" aria-hidden="true" /> */}
          <span>{uploadError}</span>
        </div>
      )}

      <Upload
        ref={uploadRef}
        files={uploadFiles}
        showFileList={false}
        batch={false}
        autoUpload={false}
        showActionButtons={false}
        multiple={false}
        withCredentials={false}
        defaultFiles={[]}
        saveUrl="noop"
        removeUrl="noop"
        restrictions={{
          allowedExtensions: ALLOWED_FILE_EXTENSIONS,
          maxFileSize: MAX_UPLOAD_FILE_SIZE_BYTES,
        }}
        onAdd={handleAdd}
        onBeforeUpload={handleBeforeUpload}
      />

      {!uploadError && (
        <div className="upload-helper-text">{t("helper_text_upload")}</div>
      )}
    </div>
  );
}
