import { useGetTemplatesGridColumnsQuery } from "@/api/templatesApiSlice";
import type { TypeGridColumn } from "@/types/column";
import { useEffect, useState } from "react";

export const useTemplateGridColumns = () => {
  const { data, isLoading: isColumnsLoading } =
    useGetTemplatesGridColumnsQuery();
  const [columns, setColumns] = useState<TypeGridColumn[]>([]);

  useEffect(() => {
    if (data && !isColumnsLoading) {
      setColumns(data?.filter((item) => item.key !== "actions"));
    }
  }, [data, isColumnsLoading]);

  return {
    columns,
    isColumnsLoading,
  };
};
