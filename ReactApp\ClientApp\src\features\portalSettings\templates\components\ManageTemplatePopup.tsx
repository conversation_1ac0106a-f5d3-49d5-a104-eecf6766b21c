import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";
import { Button } from "@progress/kendo-react-buttons";
import { useTranslation } from "react-i18next";
import CreateTemplateForm from "./CreateTemplateForm";
import CreateTemplateTreeView from "./CreateTemplateTreeView";
import { useTemplateTreeView } from "../hooks/useTemplateTreeView";
import { Loader } from "@progress/kendo-react-indicators";
import type {
  CreateTemplateFormData,
  CreateTemplateRequest,
  CreateTemplateValidation,
  SecondaryFolder,
  UpdateTemplateRequest,
} from "@/types/templates";

import type { Template } from "@/types/templates";
import logger from "@/utils/logger";

interface ManageTemplatePopupProps {
  isOpen: boolean;
  isEditMode?: boolean;
  editingTemplate?: Template | null;
  editingTemplateId?: string | null;
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  isSubmitting: boolean;
  isTemplateLoading?: boolean;
  onFieldChange: (
    _field: keyof CreateTemplateFormData,
    _value: string | boolean,
  ) => void;
  onCreate: (_payload: CreateTemplateRequest) => void;
  onUpdate?: (_payload: UpdateTemplateRequest) => void;
  onCancel: () => void;
}

export default function ManageTemplatePopup({
  isOpen,
  isEditMode = false,
  editingTemplate,
  editingTemplateId,
  formData,
  validation,
  isSubmitting,
  isTemplateLoading = false,
  onFieldChange,
  onCreate,
  onUpdate,
  onCancel,
}: ManageTemplatePopupProps) {
  const { t } = useTranslation("dashboard");
  const {
    primaryFolders,
    validation: treeValidation,
    isLoading: isTreeLoading,
    canDeleteFolder,
    addPrimaryFolder,
    addSecondaryFolder,
    editFolder,
    saveFolder,
    cancelEdit,
    deleteFolder,
    toggleExpand,
  } = useTemplateTreeView({
    templateId: editingTemplateId || undefined,
    isEditMode,
  });

  const isAnyFolderEditing = primaryFolders.some(
    (pf) =>
      pf.isEditing ||
      pf.secondaryFolders.some((sf: SecondaryFolder) => sf.isEditing),
  );

  const isCreateDisabled =
    isSubmitting ||
    !formData.templateName.trim() ||
    !!validation.nameError ||
    !!treeValidation.duplicateNameError ||
    !!treeValidation.deleteLastSecondaryError ||
    isAnyFolderEditing;

  const handleSubmitClick = () => {
    if (isEditMode) {
      // Map primaryFolders to the required parentNodes/childNodes structure for update
      const parentNodes = primaryFolders.map((pf) => ({
        ...(pf.id && pf.id.startsWith("existing-")
          ? { id: Number(pf.id.replace("existing-", "")) }
          : {}),
        name: pf.name,
        childNodes: pf.secondaryFolders.map((sf) => ({
          ...(sf.id && sf.id.startsWith("existing-")
            ? { id: Number(sf.id.replace("existing-", "")) }
            : {}),
          name: sf.name,
        })),
      }));
      const payload = {
        templateId: editingTemplateId || (editingTemplate?.templateId ?? ""),
        templateName: formData.templateName,
        templateDescription: formData.templateDescription,
        parentNodes,
        active: formData.active,
      };
      logger.info("Template Update Payload:", payload);
      onUpdate?.(payload);
    } else {
      // Map primaryFolders to the required parentNodes/childNodes structure for create
      const parentNodes = primaryFolders.map((pf) => ({
        name: pf.name,
        childNodes: pf.secondaryFolders.map((sf) => ({ name: sf.name })),
      }));
      const payload = {
        templateName: formData.templateName,
        templateDescription: formData.templateDescription,
        parentNodes,
        active: formData.active,
      };
      logger.info("Template Create Payload:", payload);
      onCreate(payload);
    }
  };

  if (!isOpen) return null;

  // Show loading indicator while template or tree data is being loaded in edit mode
  if (isEditMode && (isTemplateLoading || isTreeLoading)) {
    return (
      <Dialog
        onClose={onCancel}
        title={t("templates_popup_edit_title")}
        className="create-template-popup"
        width={950}
        height={450}
      >
        <div
          className="popup-content"
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "300px",
          }}
        >
          <Loader size="large" />
        </div>
      </Dialog>
    );
  }

  return (
    <Dialog
      onClose={onCancel}
      title={
        isEditMode
          ? t("templates_popup_edit_title")
          : t("templates_popup_create_title")
      }
      className="create-template-popup"
      width={950}
      height={450}
    >
      <div className="popup-content">
        <div className="popup-left">
          <CreateTemplateForm
            formData={formData}
            validation={validation}
            onFieldChange={onFieldChange}
          />
        </div>

        <div className="popup-right">
          <CreateTemplateTreeView
            primaryFolders={primaryFolders}
            validation={treeValidation}
            canDeleteFolder={canDeleteFolder}
            onAddPrimaryFolder={addPrimaryFolder}
            onAddSecondaryFolder={addSecondaryFolder}
            onEditFolder={editFolder}
            onSaveFolder={saveFolder}
            onCancelEdit={cancelEdit}
            onDeleteFolder={deleteFolder}
            onToggleExpand={toggleExpand}
          />
        </div>
      </div>

      <DialogActionsBar layout="end">
        <Button onClick={onCancel} disabled={isSubmitting} fillMode="flat">
          {t("cancel")}
        </Button>
        <Button
          themeColor="primary"
          onClick={handleSubmitClick}
          disabled={isCreateDisabled}
        >
          {isSubmitting ? (
            <Loader size="small" type="infinite-spinner" themeColor="primary" />
          ) : isEditMode ? (
            t("update")
          ) : (
            t("create")
          )}
        </Button>
      </DialogActionsBar>
    </Dialog>
  );
}
