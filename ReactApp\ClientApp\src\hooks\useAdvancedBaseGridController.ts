// hooks/useAdvancedGridController.ts
import { useState, useCallback } from "react";
import type {
  GridPageChangeEvent,
  GridFilterChangeEvent,
  GridSortChangeEvent,
} from "@progress/kendo-react-grid";
import type {
  CompositeFilterDescriptor,
  SortDescriptor,
} from "@progress/kendo-data-query";

export const useAdvancedBaseGridController = () => {
  const [skip, setSkip] = useState(0);
  const [take, setTake] = useState(10);
  const [filters, setFilters] = useState<CompositeFilterDescriptor>({
    logic: "and",
    filters: [],
  });
  const [sorts, setSorts] = useState<SortDescriptor[]>([]);

  const handlePageChange = useCallback((event: GridPageChangeEvent) => {
    setSkip(event.page.skip);
    setTake(event.page.take);
  }, []);

  const handleSortChange = useCallback((event: GridSortChangeEvent) => {
    setSorts(event.sort);
    setSkip(0);
  }, []);

  const handleFilterChange = useCallback((event: GridFilterChangeEvent) => {
    setFilters(event.filter);
    setSkip(0);
  }, []);

  const handleRefresh = useCallback(() => {
    setFilters({ logic: "and", filters: [] });
    setSorts([]);
    setSkip(0);
    setTake(10);
  }, []);

  return {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleSortChange,
    handleFilterChange,
    handleRefresh,
  };
};
