import { useGetClientsGridColumnsQuery } from "@/api/clientsApiSlice";
import type { TypeGridColumn } from "@/types/column";
import { useEffect, useState } from "react";

export const useClientsGridColumns = () => {
  const { data, isLoading: isColumnsLoading } = useGetClientsGridColumnsQuery();
  const [columns, setColumns] = useState<TypeGridColumn[]>([]);

  useEffect(() => {
    if (data && !isColumnsLoading) {
      setColumns(data?.filter((item) => item.key !== "actions"));
    }
  }, [data, isColumnsLoading]);

  return {
    columns,
    isColumnsLoading,
  };
};
