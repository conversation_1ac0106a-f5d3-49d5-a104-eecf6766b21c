import { XMarkIcon } from "@heroicons/react/24/outline";
import { SvgIcon } from "@progress/kendo-react-common";
import { infoCircleIcon } from "@progress/kendo-svg-icons";
import "./GridNotice.scss";

interface GridNoticeProps {
  show?: boolean;
  onClose?: () => void;
}

const GridNotice = ({ show, onClose }: GridNoticeProps) => {
  return (
    show && (
      <div className="grid-notice-container">
        <div className="grid-notice-left">
          <SvgIcon icon={infoCircleIcon} />
          <span>Please click on client to check further details</span>
        </div>
        <XMarkIcon width={16} height={16} onClick={onClose} />
      </div>
    )
  );
};

export default GridNotice;
