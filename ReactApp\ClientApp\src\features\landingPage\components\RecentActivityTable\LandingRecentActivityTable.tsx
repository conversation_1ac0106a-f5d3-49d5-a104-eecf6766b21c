import { Grid, GridColumn } from "@progress/kendo-react-grid";
import { Icon } from "@progress/kendo-react-common";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import type {
  RecentActivityTableProps,
  SetupAreaType,
} from "@/types/recentActivity";
import "./LandingRecentActivityTable.scss";
import {
  Cog6ToothIcon,
  FolderIcon,
  UserIcon,
} from "@heroicons/react/24/outline";
import dayjs from "dayjs";
import logger from "@/utils/logger";

const getSetupAreaIcon = (setupArea: SetupAreaType): React.ElementType => {
  switch (setupArea) {
    case "DMS Settings":
      return FolderIcon;
    case "Portal Settings":
      return Cog6ToothIcon;
    case "Shared Settings":
      return UserIcon;
    default:
      return Cog6ToothIcon;
  }
};

const formatDate = (dateString: string): string => {
  try {
    return dayjs(dateString).format("YYYY/MM/DD");
  } catch (error: any) {
    logger.error("Failed to format date", error);
    return dateString;
  }
};

export default function RecentActivityTable({
  data,
  totalRecords,
  dataState,
  isLoading = false,
  isFetching = false,
  onRefresh,
  onDataStateChange,
}: RecentActivityTableProps) {
  const { t } = useTranslation("dashboard");
  const navigate = useNavigate();

  // Handle refresh action
  const handleRefresh = () => {
    if (onRefresh) {
      onRefresh();
    }
  };

  type SectionClickHandler = (_relativeUrl: string) => void;

  const handleSectionClick: SectionClickHandler = (relativeUrl) => {
    if (relativeUrl) {
      navigate(relativeUrl);
    }
  };

  // Handle sort change
  const handleSortChange = (event: any) => {
    if (onDataStateChange) {
      const newDataState = {
        ...dataState,
        sort: event.sort,
      };

      onDataStateChange({ dataState: newDataState });
    }
  };

  const SetupAreaCellIcon = (props: any) => {
    const { dataItem } = props;
    const MenuIcon = getSetupAreaIcon(dataItem.setupArea);

    return (
      <td>
        <div>
          <MenuIcon className="setup-area-icon" />
        </div>
      </td>
    );
  };

  const SectionCell = (props: any) => {
    const { dataItem } = props;
    const relativeUrl = dataItem.relativeUrl;

    return (
      <td className="section-cell">
        {relativeUrl ? (
          <button
            type="button"
            className="section-link"
            onClick={() => handleSectionClick(relativeUrl)}
          >
            {dataItem.section}
          </button>
        ) : (
          <span>{dataItem.section}</span>
        )}
      </td>
    );
  };

  const LastUpdatedCell = (props: any) => {
    const { dataItem } = props;
    const formattedDate = formatDate(dataItem.lastUpdated);

    return <td>{formattedDate}</td>;
  };

  return (
    <div className="recent-activity-table">
      <div className="table-header">
        <h3 className="table-title">{t("recent_activity_title")}</h3>
      </div>
      <Grid
        data={data}
        total={totalRecords}
        style={{ height: "380px" }}
        className="recent-activity-grid"
        sortable={true}
        sort={dataState.sort}
        skip={dataState.skip}
        take={dataState.take}
        pageable={{
          buttonCount: 1,
          info: true,
          pageSizes: [20, 50, 100, 500],
          previousNext: true,
        }}
        onDataStateChange={onDataStateChange}
        onSortChange={handleSortChange}
        loader={isLoading || isFetching}
      >
        <GridColumn
          width="40px"
          cells={{
            data: SetupAreaCellIcon,
          }}
        />
        <GridColumn
          field="setupArea"
          title={t("recent_activity_columns_setup_area")}
        />
        <GridColumn
          field="section"
          title={t("recent_activity_columns_section")}
          cells={{
            data: SectionCell,
          }}
        />
        <GridColumn
          field="updatedBy"
          title={t("recent_activity_columns_updated_by")}
        />
        <GridColumn
          field="lastUpdated"
          title={t("recent_activity_columns_last_updated")}
          cells={{
            data: LastUpdatedCell,
          }}
        />
      </Grid>

      <div className="grid-refresh-container">
        <button
          className="refresh-button"
          type="button"
          onClick={handleRefresh}
          title="Refresh data"
          disabled={isLoading || isFetching}
        >
          <Icon name="refresh" />
        </button>
      </div>
    </div>
  );
}
