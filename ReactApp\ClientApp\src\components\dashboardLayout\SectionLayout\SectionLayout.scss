.dashboard-page {
  margin: 0;
  padding: 0;
  height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.dashboard-header {
  padding: 0;
  margin: 0;
  height: 3.5rem;
  border-bottom: 1px solid var(--kendo-border-color, #e0e0e0);
  background-color: var(--kendo-component-bg, #ffffff);
  border-radius: 5px;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  height: 100%;
}

.breadcrumbs {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1 1 auto;
  min-width: 0;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  font-size: var(--kendo-font-size-md, 12px);
  color: var(--kendo-body-text, #333);
}

.breadcrumb-divider {
  margin: 0 8px;
  color: var(--kendo-subtle-text, #a0aec0);
}

.header-actions {
  flex-shrink: 0;
  display: flex;
  gap: 12px;
  align-items: center;
}

.dashboard-content-card {
  padding: 12px;
  margin: 0;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--kendo-component-bg, #ffffff);
  border: 1px solid var(--kendo-border-color, #e0e0e0);
  border-radius: 4px;
}

.dashboard-main {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex: 1;
  height: 100%;
}

.dashboard-footer {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 16px;
  border-top: 1px solid var(--kendo-border-color, #e0e0e0);
  padding-top: 16px;
}

.cancel-link {
  text-decoration: underline;
  color: var(--kendo-link-text, #0078d4);
  font-size: var(--kendo-font-size-md, 14px);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  background-color: rgba(255, 255, 255, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
}

.fade-notification {
  opacity: 1;
  transition: opacity 0.5s ease-in-out;
}

.fade-notification.k-notification-hidden {
  opacity: 0;
}

@media (max-height: 800px) {
  .dashboard-main {
    gap: 8px;
    margin-bottom: 8px;
  }

  .dashboard-footer {
    padding-top: 6px;
    gap: 6px;
  }

  .cancel-link {
    font-size: calc(var(--kendo-font-size-sm, 12px));
  }
}
