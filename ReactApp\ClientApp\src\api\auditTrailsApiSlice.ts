import type {
  AuditTrailsColumn,
  AuditTrailsResponse,
} from "@/types/auditTrails";
import {
  createApi,
  type FetchBaseQueryError,
  type QueryReturnValue,
} from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";

import type {
  SortDescriptor,
  CompositeFilterDescriptor,
} from "@progress/kendo-data-query";
import { baseGridQueryParamsBuilder } from "@/utils/baseGridQueryParamsBuilder";

interface GetAuditTrailsParams {
  skip: number;
  take: number;
  filters: CompositeFilterDescriptor;
  sorts: SortDescriptor[];
}

interface GetAuditTrailsSearchOptions {
  field: string;
  value: string;
}

export const auditTrailsApiSlice = createApi({
  reducerPath: "auditTrailsApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getAuditTrailsColumns: builder.query<AuditTrailsColumn[], void>({
      queryFn: async (_arg, _api, _extra, baseQuery) => {
        const result = await baseQuery({
          url: `/api/terms-and-conditions/columns`,
          method: "GET",
          headers: {
            Accept: "text/plain",
          },
        });

        return result as QueryReturnValue<
          AuditTrailsColumn[],
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    getAuditTrails: builder.query<AuditTrailsResponse, GetAuditTrailsParams>({
      queryFn: async (
        { skip, take, filters, sorts },
        _api,
        _extra,
        baseQuery,
      ) => {
        const params = baseGridQueryParamsBuilder(skip, take, sorts, filters);

        const response = await baseQuery({
          url: `/api/terms-and-conditions/list?${params.toString()}`,
          method: "GET",
          headers: {
            COntent: "application/json",
            Accept: "application/json",
          },
        });

        return response as QueryReturnValue<
          AuditTrailsResponse,
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    downloadAuditTrail: builder.query<Blob, string>({
      async queryFn(tncId, _api, _extra, baseQuery) {
        const response = await baseQuery({
          url: `/api/terms-and-conditions/${tncId}/download`,
          method: "GET",
          headers: {
            Accept: "application/pdf",
          },
          responseHandler: (res) => res.blob(),
        });
        return { data: response.data as Blob };
      },
    }),
    getAuditTrailsSearchOptions: builder.query<
      string[],
      GetAuditTrailsSearchOptions
    >({
      queryFn: async ({ field, value }, _api, _extra, baseQuery) => {
        const result = await baseQuery({
          url: `/api/terms-and-conditions/fields/${field}?value=${encodeURIComponent(value)}&limit=5`,
          method: "GET",
          headers: {
            Accept: "application/json",
          },
        });

        return result as QueryReturnValue<string[], FetchBaseQueryError, {}>;
      },
    }),
  }),
});

export const {
  useGetAuditTrailsColumnsQuery,
  useGetAuditTrailsQuery,
  useLazyDownloadAuditTrailQuery,
  useLazyGetAuditTrailsSearchOptionsQuery,
} = auditTrailsApiSlice;
