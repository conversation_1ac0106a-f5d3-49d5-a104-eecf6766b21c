import { Chip } from "@progress/kendo-react-buttons";
import type {
  CompositeFilterDescriptor,
  FilterDescriptor,
} from "@progress/kendo-data-query";
import type { ColumnConfig } from "@/types/column";
import "./BaseGridAppliedFilters.scss";

interface BaseGridAppliedFiltersProps {
  filters: CompositeFilterDescriptor;
  columns: ColumnConfig[];
  onRemoveFilter: (_field: string) => void;
}

export default function BaseGridAppliedFilters({
  filters,
  columns,
  onRemoveFilter,
}: BaseGridAppliedFiltersProps) {
  const extractFilters = (
    filters: CompositeFilterDescriptor,
  ): FilterDescriptor[] => {
    const result: FilterDescriptor[] = [];

    filters?.filters?.forEach((f: any) => {
      if (f.filters) {
        result.push(...extractFilters(f));
      } else {
        result.push(f);
      }
    });

    return result;
  };

  const activeFilters = extractFilters(filters);

  if (activeFilters.length === 0) return null;

  const getDisplayName = (field: string): string => {
    const col = columns.find((c) => c.key === field);
    return col?.displayValue || field;
  };

  const formatRange = (start: any, end: any): string => {
    return `${start ?? "-"} → ${end ?? "-"}`;
  };

  const formatDate = (dateStr: string | null) =>
    dateStr ? new Date(dateStr).toLocaleDateString() : "-";

  const isDateString = (v: any): boolean =>
    typeof v === "string" && !isNaN(Date.parse(v));

  const renderValue = (val: any, field?: string): string => {
    // Range object
    if (val && typeof val === "object" && "start" in val && "end" in val) {
      const { start, end } = val;

      if (field === "fileSize") {
        const toMB = (bytes: number) =>
          `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
        return `${toMB(start ?? 0)} → ${toMB(end ?? 0)}`;
      }

      if (isDateString(start) || isDateString(end)) {
        return `${formatDate(start)} → ${formatDate(end)}`;
      }

      return formatRange(start, end);
    }

    // File size (not a range)
    if (field === "fileSize" && typeof val === "number") {
      return `${(val / (1024 * 1024)).toFixed(2)} MB`;
    }

    // Arrays
    if (Array.isArray(val)) {
      return val
        .map((v) => (typeof v === "object" ? JSON.stringify(v) : String(v)))
        .join(", ");
    }

    // Objects
    if (typeof val === "object" && val !== null) {
      return JSON.stringify(val);
    }

    // Primitive
    return String(val);
  };

  return (
    <div className="applied-filters">
      <strong>Filter By:</strong>
      <div className="chip-container">
        {activeFilters.map((filter) => (
          <Chip
            id={String(filter?.field ?? "")}
            key={String(filter?.field ?? "")}
            removable
            onRemove={() => onRemoveFilter(filter.field as string)}
            rounded="full"
            themeColor="success"
          >
            <span className="column-name">
              {getDisplayName(filter.field as string)}
            </span>
            <span className="filter-divider">:</span>
            <span>{renderValue(filter.value, filter.field as string)}</span>
          </Chip>
        ))}
      </div>
    </div>
  );
}
