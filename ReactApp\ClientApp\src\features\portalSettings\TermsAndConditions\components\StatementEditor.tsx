import { Card } from "@progress/kendo-react-layout";
import { TextArea } from "@progress/kendo-react-inputs";
import type { TextAreaChangeEvent } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";

interface Props {
  value: string;
  onChange: (_newValue: string) => void;
}

export default function StatementEditor({ value, onChange }: Props) {
  const { t } = useTranslation("dashboard");

  return (
    <div className="statement-editor">
      <div className="statement-wrapper">
        {t("statement_title")} <span style={{ color: "red" }}>**</span>
      </div>
      <Card className="statement-card">
        <TextArea
          rows={2}
          value={value}
          onChange={(e: TextAreaChangeEvent) => onChange(e.value)}
          className="text-sm"
        />
      </Card>
      <div className="statement-helper">{t("statement_helper")}</div>
    </div>
  );
}
