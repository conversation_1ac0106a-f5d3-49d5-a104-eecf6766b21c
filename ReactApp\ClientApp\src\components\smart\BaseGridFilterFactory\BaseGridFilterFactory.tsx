import type { FC } from "react";
import { HeaderTdElement } from "@progress/kendo-react-data-tools";
import type { GridCustomFilterCellProps } from "@progress/kendo-react-grid";
import type { ColumnConfig } from "@/types/column";
import AdvancedAutoComplete from "@/components/AdvancedAutoComplete/AdvancedAutoComplete";
import MultiSelector from "@/components/MultiSelector/MultiSelector";
import FileSizeSelector from "@/components/FileSizeSelector/FileSizeSelector";
import DateRangeSelector from "@/components/DateRangeSelector/DateRangeSelector";

interface Props extends GridCustomFilterCellProps {
  column: ColumnConfig;
  useAutoSuggestHook?: () => {
    fetchSuggestions: (_field: string, _value: string) => Promise<string[]>;
    isLoading: boolean;
  };
  dataRangeMode?: boolean; // Optional flag to convert MB to Bytes
}

const BaseGridFilterFactory: FC<Props> = ({
  column,
  useAutoSuggestHook,
  dataRangeMode,
  ...props
}) => {
  const { columnId } = props.thProps || {};
  const { key, filterType, filterData = [] } = column;

  const renderComponent = () => {
    switch (filterType) {
      case "search":
        return (
          <AdvancedAutoComplete
            {...props}
            field={key}
            useAutoSuggestHook={useAutoSuggestHook!}
          />
        );
      case "multiSelect":
        return (
          <MultiSelector
            {...props}
            data={filterData}
            dataTextField={Object.keys(filterData[0] || {})[0] || "name"}
            dataValueField={Object.keys(filterData[0] || {})[1] || "value"}
          />
        );
      case "range":
        if (key === "fileSize")
          return <FileSizeSelector {...props} dataRangeMode={dataRangeMode} />;
        if (key.includes("Date") || key.includes("On"))
          return <DateRangeSelector {...props} />;
        return null;
      default:
        return null;
    }
  };

  return (
    <HeaderTdElement columnId={columnId || ""} {...props.thProps}>
      {renderComponent()}
    </HeaderTdElement>
  );
};

export default BaseGridFilterFactory;
