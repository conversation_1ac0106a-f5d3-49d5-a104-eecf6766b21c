import {
  createApi,
  type FetchBaseQueryError,
  type QueryReturnValue,
} from "@reduxjs/toolkit/query/react";
import { baseQueryWithReauth } from "./interceptorsSlice";
import type { BaseGridParams, TypeGridColumn } from "@/types/column";
import httpVerbs from "@/utils/http/httpVerbs";
import appConfig from "@/config";
import { mockClientsColumns, mockClientsData } from "./mocks/clientsMockData";
import { baseGridQueryParamsBuilder } from "@/utils/baseGridQueryParamsBuilder";
import type { ClientListResponse } from "@/types/clients";

export const clientsApiSlice = createApi({
  reducerPath: "clientsApiSlice",
  baseQuery: baseQueryWithReauth,
  tagTypes: ["Clients"],
  endpoints: (builder) => ({
    getClientsGridColumns: builder.query<TypeGridColumn[], void>({
      queryFn: async (_arg, _api, _extra, baseQuery) => {
        if (appConfig.featureFlags.api.CLIENTS_API_USE_MOCK) {
          await new Promise((resolve) => setTimeout(resolve, 500));

          return {
            data: mockClientsColumns,
          };
        }
        const result = await baseQuery({
          url: "/api/clients/columns",
          method: httpVerbs.GET,
          headers: {
            Accept: "text/plain",
          },
          //meta: OperationalServiceTypes.PortalService,
        });

        return result as QueryReturnValue<
          TypeGridColumn[],
          FetchBaseQueryError,
          {}
        >;
      },
    }),
    getClientList: builder.query<ClientListResponse, BaseGridParams>({
      queryFn: async (
        { skip, take, filters, sorts },
        _api,
        _extra,
        baseQuery,
      ) => {
        if (appConfig.featureFlags.api.CLIENTS_API_USE_MOCK) {
          await new Promise((resolve) => setTimeout(resolve, 500));

          return {
            data: {
              records: mockClientsData,
              pageCount: 1,
              pageNumber: 10,
              pageSize: mockClientsData.length,
              totalRecordCount: mockClientsData.length,
            },
          };
        }
        const params = baseGridQueryParamsBuilder(skip, take, sorts, filters);

        const response = await baseQuery({
          url: "/api/clients",
          method: httpVerbs.GET,
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          params: params,
          // meta: OperationalServiceTypes.PortalService,
        });

        return response as QueryReturnValue<
          ClientListResponse,
          FetchBaseQueryError,
          {}
        >;
      },
    }),
  }),
});

export const { useGetClientsGridColumnsQuery, useGetClientListQuery } =
  clientsApiSlice;
