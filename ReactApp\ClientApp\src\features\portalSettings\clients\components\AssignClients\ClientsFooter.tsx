import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@progress/kendo-react-buttons";

interface ClientsFooterProps {
  onCancel: () => void;
  onConfirm: () => void;
  selectedTemplate: string | null;
}

export default function ClientsFooter({
  onCancel,
  onConfirm,
  selectedTemplate,
}: ClientsFooterProps) {
  const { t } = useTranslation("dashboard");

  return (
    <div className="clients-footer-buttons">
      <Button onClick={onCancel}>{t("cancel")}</Button>
      <Button 
        themeColor="primary" 
        onClick={onConfirm} 
        disabled={!selectedTemplate}
      >
        {t("confirm")}
      </Button>
    </div>
  );
}
