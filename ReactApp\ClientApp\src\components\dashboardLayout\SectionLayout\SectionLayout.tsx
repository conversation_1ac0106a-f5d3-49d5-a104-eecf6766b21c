import { useEffect, useState, type ReactNode } from "react";
import { useTranslation } from "react-i18next";
import { useLocation } from "react-router-dom";
import { Card } from "@progress/kendo-react-layout";
import { Loader } from "@progress/kendo-react-indicators";
import {
  Notification,
  NotificationGroup,
} from "@progress/kendo-react-notification";
import "./SectionLayout.scss";
import { menuData } from "@/constants/menuData";

interface SectionLayoutProps {
  isLoading?: boolean;
  isFetching?: boolean;
  isSaving?: boolean;
  errorMessage?: string;
  onCloseError?: () => void;
  successMessage?: string;
  onCloseSuccess?: () => void;
  headerActions?: ReactNode;
  children: ReactNode;
  footer?: ReactNode;
}

export default function SectionLayout({
  isLoading = false,
  isFetching = false,
  isSaving = false,
  errorMessage = "",
  onCloseError,
  successMessage = "",
  onCloseSuccess,
  headerActions,
  children,
  footer,
}: SectionLayoutProps) {
  const { t } = useTranslation("dashboard");
  const location = useLocation();

  const path = location.pathname;
  const isDashboard = path === "/dashboard";

  const matchedMenu = menuData.find((menu) =>
    menu.children.some((child) => child.route === path),
  );
  const matchedChild = matchedMenu?.children.find(
    (child) => child.route === path,
  );

  const breadcrumbKeys = [
    "portal",
    matchedMenu?.title || "",
    matchedChild?.title || "",
  ].filter(Boolean);

  const showLoader = isLoading || isFetching || isSaving;

  const [showError, setShowError] = useState(!!errorMessage);
  const [showSuccess, setShowSuccess] = useState(!!successMessage);

  useEffect(() => {
    setShowError(!!errorMessage);
    if (errorMessage) {
      const timer = setTimeout(() => {
        setShowError(false);
        onCloseError?.();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage, onCloseError]);

  useEffect(() => {
    setShowSuccess(!!successMessage);
    if (successMessage) {
      const timer = setTimeout(() => {
        setShowSuccess(false);
        onCloseSuccess?.();
      }, 4000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, onCloseSuccess]);

  return (
    <div className="dashboard-page">
      {showLoader && (
        <div className="loading-overlay">
          <Loader size="large" type="infinite-spinner" themeColor="primary" />
        </div>
      )}

      {showError && (
        <NotificationGroup style={{ right: 10, top: 10 }}>
          <Notification
            className="fade-notification"
            type={{ style: "error", icon: true }}
            closable={true}
            onClose={() => {
              setShowError(false);
              onCloseError?.();
            }}
          >
            <span>{errorMessage}</span>
          </Notification>
        </NotificationGroup>
      )}

      {showSuccess && (
        <NotificationGroup style={{ right: 10, top: 60 }}>
          <Notification
            className="fade-notification"
            type={{ style: "success", icon: true }}
            closable={true}
            onClose={() => {
              setShowSuccess(false);
              onCloseSuccess?.();
            }}
          >
            <span>{successMessage}</span>
          </Notification>
        </NotificationGroup>
      )}

      <Card className="dashboard-header">
        <div className="header-content">
          <div className="breadcrumbs">
            {isDashboard ? (
              <h3>{t("dashboard")}</h3>
            ) : (
              breadcrumbKeys.map((key, index) => (
                <div key={key} className="breadcrumb-item">
                  {index !== 0 && <span className="breadcrumb-divider">/</span>}
                  <span>{t(`${key}`)}</span>
                </div>
              ))
            )}
          </div>
          <div className="header-actions">{headerActions}</div>
        </div>
      </Card>

      <Card className="dashboard-content-card">
        <div className="dashboard-main">{children}</div>
        {footer && <div className="dashboard-footer">{footer}</div>}
      </Card>
    </div>
  );
}
