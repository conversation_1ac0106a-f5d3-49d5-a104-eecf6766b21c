import { But<PERSON> } from "@progress/kendo-react-buttons";
import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";
import { useTranslation } from "react-i18next";

interface TemplateDeleteDialogProps {
  open: boolean;
  onClose: () => void;
}

const TemplateDeleteDialog = ({ open, onClose }: TemplateDeleteDialogProps) => {
  const { t } = useTranslation("dashboard");

  return (
    <>
      {open && (
        <Dialog onClose={onClose} closeIcon={true} title="">
          <p style={{ margin: "25px", textAlign: "center" }}>
            {t("templates_delete_confirm_message")}
          </p>
          <DialogActionsBar>
            <Button type="button" onClick={onClose}>
              {t("templates_delete_yes_button")}
            </Button>
            <Button type="button" onClick={onClose} themeColor="primary">
              {t("templates_delete_no_button")}
            </Button>
          </DialogActionsBar>
        </Dialog>
      )}
    </>
  );
};

export default TemplateDeleteDialog;
