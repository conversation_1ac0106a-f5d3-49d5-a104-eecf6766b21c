import { Card } from "@progress/kendo-react-layout";
import { Input } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";

interface Props {
  name: string;
  description: string;
  onChange: (_field: "name" | "description", _value: string) => void;
}

export default function SampleForm({ name, description, onChange }: Props) {
  const { t } = useTranslation("dashboard");

  return (
    <Card className="sample-form">
      <h3>{t("title")}</h3>

      <div className="form-group">
        <label htmlFor="sample-name">{t("name")}</label>
        <Input
          id="sample-name"
          name="name"
          value={name}
          onChange={(e) => onChange("name", e.value)}
          placeholder={t("placeholder")}
        />
      </div>

      <div className="form-group">
        <label htmlFor="sample-description">{t("description")}</label>
        <Input
          id="sample-description"
          name="description"
          value={description}
          onChange={(e) => onChange("description", e.value)}
          placeholder={t("description_placeholder")}
        />
      </div>
    </Card>
  );
}
