import { useState, useCallback, useEffect } from "react";
import { useGetTemplateByIdQuery } from "@/api/templatesApiSlice";
import type { PrimaryFolder, TreeValidation } from "@/types/templates";

interface useTemplateTreeViewProps {
  templateId?: string;
  isEditMode?: boolean;
}

// Helper to generate a unique folder ID
const generateUniqueId = () => {
  const timestamp = Date.now();
  const randomPart = Math.random().toString(36).slice(2, 10);
  return `folder-${timestamp}-${randomPart}`;
};

// Helper to collect all folder names (primary and secondary) except a given folderId
const collectAllNames = (
  primaryFolders: PrimaryFolder[],
  excludeId?: string,
) => {
  const names: string[] = [];
  primaryFolders.forEach((pf) => {
    if (pf.id !== excludeId) names.push(pf.name.toLowerCase());
    pf.secondaryFolders.forEach((sf) => {
      if (sf.id !== excludeId) names.push(sf.name.toLowerCase());
    });
  });
  return names;
};

// Helper to collect all secondary folder names within a primary folder (optionally excluding a folderId)
const collectSecondaryNames = (
  primaryFolder: PrimaryFolder,
  excludeId?: string,
) => {
  return primaryFolder.secondaryFolders
    .filter((sf) => sf.id !== excludeId)
    .map((sf) => sf.name.toLowerCase());
};

// Helper to generate a unique folder name
const generateUniqueName = (
  baseName: string,
  existingNames: string[],
): string => {
  if (!existingNames.includes(baseName.toLowerCase())) {
    return baseName;
  }
  let counter = 1;
  let name = `${baseName}(${counter})`;
  while (existingNames.includes(name.toLowerCase())) {
    counter++;
    name = `${baseName}(${counter})`;
  }
  return name;
};

export function useTemplateTreeView({
  templateId,
  isEditMode = false,
}: useTemplateTreeViewProps = {}) {
  const { data: templateData, isLoading, isError } = useGetTemplateByIdQuery(
    templateId!,
    {
      refetchOnMountOrArgChange: true,
      skip: !isEditMode || !templateId,
    },
  );

  const [primaryFolders, setPrimaryFolders] = useState<PrimaryFolder[]>([]);
  const [newFolderIds, setNewFolderIds] = useState<Set<string>>(new Set());
  const [validation, setValidation] = useState<TreeValidation>({
    duplicateNameError: "",
    deleteLastSecondaryError: "",
  });

  // Initialize folders based on edit mode and template data
  useEffect(() => {
    if (
      isEditMode &&
      templateData &&
      templateData.nodes &&
      templateData.nodes.length > 0
    ) {
      const convertedFolders: PrimaryFolder[] = templateData.nodes.map(
        (node) => ({
          id: `existing-${node.id}`,
          name: node.name,
          isEditing: false,
          expanded: true,
          secondaryFolders: node.childNodes.map((child) => ({
            id: `existing-${child.id}`,
            name: child.name,
            isEditing: false,
          })),
        }),
      );
      setPrimaryFolders(convertedFolders);
    } else if (!isEditMode) {
      const defaultPrimaryId = generateUniqueId();
      const defaultSecondaryId = generateUniqueId();
      setPrimaryFolders([
        {
          id: defaultPrimaryId,
          name: "Primary Folder",
          isEditing: false,
          expanded: true,
          secondaryFolders: [
            {
              id: defaultSecondaryId,
              name: "Secondary Folder",
              isEditing: false,
            },
          ],
        },
      ]);
      setNewFolderIds(new Set([defaultPrimaryId, defaultSecondaryId]));
    }
  }, [isEditMode, templateData]);

  const clearValidationErrors = useCallback(() => {
    setValidation({
      duplicateNameError: "",
      deleteLastSecondaryError: "",
    });
  }, []);

  const isExistingFolder = useCallback(
    (folderId: string) => folderId.startsWith("existing-"),
    [],
  );

  const canDeleteFolder = useCallback(
    (folderId: string) => {
      if (isEditMode) {
        return newFolderIds.has(folderId);
      }
      return true;
    },
    [isEditMode, newFolderIds],
  );

  // Add a new primary folder with a unique name
  const addPrimaryFolder = useCallback(() => {
    const newId = generateUniqueId();
    const secondaryId = generateUniqueId();
    const existingNames = primaryFolders.map((pf) => pf.name.toLowerCase());
    const uniquePrimaryName = generateUniqueName(
      "Primary Folder",
      existingNames,
    );
    setPrimaryFolders((prev) => [
      ...prev,
      {
        id: newId,
        name: uniquePrimaryName,
        isEditing: false,
        expanded: true,
        secondaryFolders: [
          {
            id: secondaryId,
            name: "Secondary Folder",
            isEditing: false,
          },
        ],
      },
    ]);
    setNewFolderIds((prev) => new Set([...prev, newId, secondaryId]));
    clearValidationErrors();
  }, [primaryFolders, clearValidationErrors]);

  // Add a new secondary folder to a primary folder
  const addSecondaryFolder = useCallback(
    (primaryFolderId: string) => {
      const newId = generateUniqueId();
      const primaryFolder = primaryFolders.find(
        (pf) => pf.id === primaryFolderId,
      );
      if (!primaryFolder) return;
      const existingNames = collectSecondaryNames(primaryFolder);
      const uniqueSecondaryName = generateUniqueName(
        "Secondary Folder",
        existingNames,
      );
      setPrimaryFolders((prev) =>
        prev.map((folder) =>
          folder.id === primaryFolderId
            ? {
                ...folder,
                secondaryFolders: [
                  ...folder.secondaryFolders,
                  {
                    id: newId,
                    name: uniqueSecondaryName,
                    isEditing: false,
                  },
                ],
              }
            : folder,
        ),
      );
      setNewFolderIds((prev) => new Set([...prev, newId]));
      clearValidationErrors();
    },
    [primaryFolders, clearValidationErrors],
  );

  // Set a folder (primary or secondary) to editing mode
  const editFolder = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      setPrimaryFolders((prev) =>
        prev.map((folder) => {
          if (!isSecondary && folder.id === folderId) {
            return { ...folder, isEditing: true };
          }
          if (isSecondary && folder.id === primaryFolderId) {
            return {
              ...folder,
              secondaryFolders: folder.secondaryFolders.map((sf) =>
                sf.id === folderId ? { ...sf, isEditing: true } : sf,
              ),
            };
          }
          return folder;
        }),
      );
      clearValidationErrors();
    },
    [clearValidationErrors],
  );

  const saveFolder = useCallback(
    (
      folderId: string,
      newName: string,
      isSecondary: boolean,
      primaryFolderId?: string,
    ): boolean => {
      if (!newName.trim()) return false;
      if (isSecondary && primaryFolderId) {
        const primaryFolder = primaryFolders.find(
          (pf) => pf.id === primaryFolderId,
        );
        if (!primaryFolder) return false;
        const secondaryNames = collectSecondaryNames(primaryFolder, folderId);
        if (secondaryNames.includes(newName.toLowerCase())) {
          setValidation((prev) => ({
            ...prev,
            duplicateNameError: "Folder name already exists",
          }));
          return false;
        }
      } else {
        const allNames = collectAllNames(primaryFolders, folderId);
        if (allNames.includes(newName.toLowerCase())) {
          setValidation((prev) => ({
            ...prev,
            duplicateNameError: "Folder name already exists",
          }));
          return false;
        }
      }
      setPrimaryFolders((prev) =>
        prev.map((folder) => {
          if (!isSecondary && folder.id === folderId) {
            return { ...folder, name: newName, isEditing: false };
          }
          if (isSecondary && folder.id === primaryFolderId) {
            return {
              ...folder,
              secondaryFolders: folder.secondaryFolders.map((sf) =>
                sf.id === folderId
                  ? { ...sf, name: newName, isEditing: false }
                  : sf,
              ),
            };
          }
          return folder;
        }),
      );
      clearValidationErrors();
      return true;
    },
    [primaryFolders, clearValidationErrors],
  );

  // Cancel editing mode for a folder
  const cancelEdit = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      setPrimaryFolders((prev) =>
        prev.map((folder) => {
          if (!isSecondary && folder.id === folderId) {
            return { ...folder, isEditing: false };
          }
          if (isSecondary && folder.id === primaryFolderId) {
            return {
              ...folder,
              secondaryFolders: folder.secondaryFolders.map((sf) =>
                sf.id === folderId ? { ...sf, isEditing: false } : sf,
              ),
            };
          }
          return folder;
        }),
      );
      clearValidationErrors();
    },
    [clearValidationErrors],
  );

  // Delete a folder (primary or secondary), with validation for last-folder constraints
  const deleteFolder = useCallback(
    (folderId: string, isSecondary: boolean, primaryFolderId?: string) => {
      if (!canDeleteFolder(folderId)) return;
      if (isSecondary) {
        const primaryFolder = primaryFolders.find(
          (pf) => pf.id === primaryFolderId,
        );
        if (primaryFolder && primaryFolder.secondaryFolders.length === 1) {
          setValidation((prev) => ({
            ...prev,
            deleteLastSecondaryError:
              "Cannot delete the last secondary folder. Each primary folder must have at least one secondary folder.",
          }));
          return;
        }
        setPrimaryFolders((prev) =>
          prev.map((folder) =>
            folder.id === primaryFolderId
              ? {
                  ...folder,
                  secondaryFolders: folder.secondaryFolders.filter(
                    (sf) => sf.id !== folderId,
                  ),
                }
              : folder,
          ),
        );
      } else {
        // Prevent deleting the last primary folder
        if (primaryFolders.length === 1) {
          setValidation((prev) => ({
            ...prev,
            deleteLastSecondaryError:
              "Cannot delete the last primary folder. At least one primary folder is required.",
          }));
          return;
        }
        setPrimaryFolders((prev) =>
          prev.filter((folder) => folder.id !== folderId),
        );
      }
      setNewFolderIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(folderId);
        return newSet;
      });
      clearValidationErrors();
    },
    [primaryFolders, canDeleteFolder, clearValidationErrors],
  );

  // Toggle expand/collapse for a primary folder
  const toggleExpand = useCallback((primaryFolderId: string) => {
    setPrimaryFolders((prev) =>
      prev.map((folder) =>
        folder.id === primaryFolderId
          ? { ...folder, expanded: !folder.expanded }
          : folder,
      ),
    );
  }, []);

  return {
    primaryFolders,
    validation,
    isLoading,
    isError,
    isExistingFolder,
    canDeleteFolder,
    addPrimaryFolder,
    addSecondaryFolder,
    editFolder,
    saveFolder,
    cancelEdit,
    deleteFolder,
    toggleExpand,
  };
}
