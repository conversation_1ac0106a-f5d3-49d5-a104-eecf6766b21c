import { useDeleteTemplateMutation } from "@/api/templatesApiSlice";
import { Button } from "@progress/kendo-react-buttons";
import { Dialog } from "@progress/kendo-react-dialogs";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";
import {
  NotificationGroup,
  Notification,
} from "@progress/kendo-react-notification";
import { Fade } from "@progress/kendo-react-animation";
import { useState } from "react";
import "./TemplateDeleteDialog.scss";
import { useTranslation } from "react-i18next";
import { Loader } from "@progress/kendo-react-indicators";

interface TemplateDeleteDialogProps {
  open: boolean;
  onClose: () => void;
  selectedTemplateId?: string | undefined;
  refetch: () => void;
}

const TemplateDeleteDialog = ({
  open,
  onClose,
  selectedTemplateId,
  refetch,
}: TemplateDeleteDialogProps) => {
  const [deleteTemplate, { isLoading }] = useDeleteTemplateMutation();
  const [showError, setShowError] = useState(false);
  const { t } = useTranslation("dashboard");
  const handleDelete = async () => {
    try {
      await deleteTemplate(selectedTemplateId as string).unwrap();
      onClose();
      refetch();
    } catch {
      onClose();
      setShowError(true);
      setTimeout(() => setShowError(false), 3000);
    }
  };

  return (
    <>
      {open && (
        <>
          {isLoading ? <Loader size="large" type={"pulsing"} /> : null}
          <Dialog
            onClose={onClose}
            title=" "
            width={500}
            className="template-delete-dialog"
          >
            <div className="dialog-content">
              <div className="icon-wrapper">
                <ExclamationTriangleIcon className="warning-icon" />
              </div>
              <p className="dialog-title">
                {t("templates_delete_confirm_message")}
              </p>
              <p className="dialog-subtext">
                {t("templates_delete_confirm_message_subtext")}
              </p>
            </div>
            <div className="dialog-actions-bar">
              <Button
                themeColor="primary"
                fillMode="solid"
                onClick={handleDelete}
                disabled={isLoading}
                icon={isLoading ? "loading" : undefined}
              >
                {t("templates_delete_yes_button")}
              </Button>
              <Button
                themeColor="primary"
                fillMode="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                {t("templates_delete_no_button")}
              </Button>
            </div>
          </Dialog>
        </>
      )}

      {showError && (
        <NotificationGroup style={{ position: "fixed", top: 20, right: 20 }}>
          <Fade>
            <Notification
              type={{ style: "error", icon: true }}
              closable={true}
              onClose={() => setShowError(false)}
            >
              {t("templates_delete_error")}
            </Notification>
          </Fade>
        </NotificationGroup>
      )}
    </>
  );
};

export default TemplateDeleteDialog;
