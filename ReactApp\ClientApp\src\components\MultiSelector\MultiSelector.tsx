import type { GridFilterCellProps } from "@progress/kendo-react-grid";
import {
  MultiSelect,
  type MultiSelectChangeEvent,
} from "@progress/kendo-react-dropdowns";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import "./MultiSelector.scss";

interface MultiSelectorProps extends GridFilterCellProps {
  data: any[];
  dataTextField?: string;
  dataValueField?: string;
}

const MultiSelector = ({
  data,
  value,
  onChange,
  dataTextField = "label",
  dataValueField = "value",
}: MultiSelectorProps) => {
  const hasValue = (val: any) => Array.isArray(val) && val.length > 0;

  const handleChange = (event: MultiSelectChangeEvent) => {
    const selectedItems = event.value;
    const selectedValues = selectedItems.map(
      (item: any) => item[dataValueField],
    );

    onChange?.({
      value: selectedValues,
      operator: selectedValues.length > 0 ? "eq" : "",
      syntheticEvent: event.syntheticEvent,
    });
  };

  const handleClear = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    onChange?.({
      value: null,
      operator: "",
      syntheticEvent: event,
    });
  };

  const selectedValues = Array.isArray(value) ? value : [];

  return (
    <div className="gridCellFilterContainer">
      <MultiSelect
        data={data}
        textField={dataTextField}
        dataItemKey={dataValueField}
        value={data.filter((item) =>
          selectedValues.includes(item[dataValueField]),
        )}
        onChange={handleChange}
        filterable
        fillMode="solid"
      />
      <Button
        title="Clear"
        disabled={!hasValue(value)}
        onClick={handleClear}
        svgIcon={filterClearIcon}
      />
    </div>
  );
};

export default MultiSelector;
