.create-template-popup {
  .k-dialog {
    padding: 0;
    margin: 0;
  }

  .k-dialog-actions {
    background-color: white;
  }

  &.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar,
  .k-dialog-titlebar {
    background-color: #007acc;
    color: white;
    border: none;
    border-bottom: none;
    border-top: none;

    .k-dialog-title,
    .k-window-title.k-dialog-title {
      color: white;
      font-weight: 600;
      font-size: 18px;
      margin: 0;
    }

    .k-dialog-close,
    .k-window-titlebar-action.k-dialog-titlebar-action {
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      border: none;
      background: transparent;

      &:hover {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
      }

      .k-icon,
      .k-button-icon {
        color: white;
      }
    }
  }

  .k-dialog-content {
    padding: 0;
    margin: 0;
    overflow: hidden;
    display: flex;
    height: calc(100% - 100px);
  }

  .popup-content {
    display: flex;
    width: 100%;
    height: 100%;

    .popup-left {
      flex: 1;
      padding: 20px;
      border-right: 1px solid #e0e0e0;
      min-width: 0;
    }

    .popup-right {
      flex: 0 0 500px;
      max-width: 500px;
      padding: 20px;
      background-color: white;
      overflow-x: hidden;
    }
  }
}

.create-template-tree-view {
  .tree-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .k-button,
    .add-primary-folder-btn {
      font-size: 12px;
      padding: 6px 12px;
      color: #007acc;

      &:hover {
        background-color: #f0f8ff;
        border-color: #005a99;
        color: #005a99;
      }
    }
  }

  .validation-error {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 14px;
    margin-bottom: 8px;
    padding: 8px;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    gap: 8px;

    .validation-error-icon {
      color: #dc3545; 
      flex-shrink: 0; 
    }
  }

  .tree-container {
    .k-treeview {
      .k-treeview-item {
        .k-treeview-leaf,
        .k-treeview-top,
        .k-treeview-mid,
        .k-treeview-bot {
          padding: 0;
          margin: 0;
          width: 100%;

          .k-treeview-leaf-text,
          .k-treeview-text {
            padding: 0;
            margin: 0;
            width: 100%;
          }
        }
      }
    }

    .tree-item {
      width: 100%;

      .folder-content {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        position: relative;

        .folder-icon {
          width: 16px;
          height: 16px;
          color: #666;
          flex-shrink: 0;
          margin-left: 2px;
          margin-right: 2px;
        }

        .folder-name {
          flex: 1;
          font-size: 14px;
          color: #333;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          min-width: 0;
          max-width: calc(100% - 120px);

          .folder-name-text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: inline-block;
            max-width: calc(100% - 30px);
            vertical-align: top;
          }
        }

        .folder-actions {
          display: flex;
          gap: 4px;
          margin-left: auto;
          width: 80px;

          .k-button {
            padding: 3px;
            min-width: auto;
            width: 22px;
            height: 22px;
            border: none;
            background: transparent;
            color: #007acc;

            &:hover {
              background-color: rgba(0, 122, 204, 0.1);
              color: #005a99;
            }

            .k-icon,
            .k-svg-icon {
              width: 16px;
              height: 16px;
              color: inherit;
            }
          }
        }

        .edit-container {
          display: flex;
          align-items: center;
          gap: 6px;
          flex: 1;
          width: 100%;
          max-width: calc(100% - 30px);

          .edit-input {
            flex: 1;
            max-width: calc(100% - 40px);

            .k-input {
              font-size: 14px;
              padding: 2px 6px;
              height: 24px;
              border: 1px solid #ccc;
              width: 100%;
            }
          }

          .edit-actions {
            display: flex;
            gap: 4px;
            flex-shrink: 0;

            .k-button {
              padding: 4px;
              min-width: auto;
              width: 24px;
              height: 24px;
              border: none;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #007acc;

              &:hover {
                background-color: rgba(0, 122, 204, 0.1);
                color: #005a99;
              }

              .k-icon,
              .k-svg-icon {
                width: 14px;
                height: 14px;
                color: inherit;
              }
            }
          }
        }
      }

      &.primary-folder {
        .folder-content {
          padding: 4px 8px;
          padding-left: 2px;
          border-bottom: 1px solid #e0e0e0;

          .folder-name {
            font-weight: 600;
            color: #333;
            font-size: 14px;
          }

          .folder-icon {
            color: #666;
          }
        }
      }

      &.secondary-folder {
        margin-left: 24px;

        .folder-content {
          padding: 4px 8px;
          padding-left: 2px;

          .folder-name {
            font-weight: 400;
            color: #666;
            font-size: 14px;
          }

          .folder-icon {
            color: #666;
          }
        }
      }
    }
  }

  .k-dialog-actions {
    padding: 12px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 8px;

    .k-button {
      padding: 8px 16px;
      font-weight: 500;
      border-radius: 4px;
      min-width: 80px;
      font-size: 14px;

      &.k-button-solid-primary {
        background-color: #007acc;
        border-color: #007acc;
        color: white;

        &:hover {
          background-color: #005a99;
          border-color: #005a99;
        }
      }

      &.k-button-solid-base {
        background-color: transparent;
        border: none;
        color: #333;

        &:hover {
          background-color: #f5f5f5;
        }
      }
    }
  }
}

.create-template-form {
  .form-group {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
  }

  .validation-error {
    color: #dc3545;
    font-size: 12px;
  }

  .switch-group {
    .switch-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .switch-label {
        font-weight: 500;
        color: #333;
        font-size: 14px;
      }

      .k-switch {
        margin-left: auto;
      }
    }
  }
}

