import type { GridCustomCellProps } from "@progress/kendo-react-grid";
import type { ReactNode } from "react";
import { TemplateStatus } from "../components/TemplateStatus";
import DownloadTemplate from "../components/DownloadTemplate";

export const templateGridDataCellMapper: Record<
  string,
  (_props: GridCustomCellProps) => ReactNode
> = {
  active: ({ dataItem }) => {
    const value = dataItem.active;
    return TemplateStatus(String(Boolean(value)));
  },
  assignedClientsCsvUrl: (props: GridCustomCellProps) => (
    <DownloadTemplate {...props} />
  ),
};
