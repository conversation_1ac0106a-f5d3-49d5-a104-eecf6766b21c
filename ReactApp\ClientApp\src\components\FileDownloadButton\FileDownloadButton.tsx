import type { ReactNode } from "react";
import "./FileDownloadButton.scss";

interface FileDownloadButtonProps {
  fileId: string | number;
  triggerDownload: (_fileId: string | number) => Promise<Blob>;
  fileName: string;
  disabled?: boolean;
  buttonText?: string;
  fallbackText?: ReactNode;
  mimeType?: string;
  fileExtension?: string;
}

export default function FileDownloadButton({
  fileId,
  triggerDownload,
  fileName,
  disabled = false,
  buttonText = "Download",
  mimeType = "application/octet-stream",
  fileExtension,
}: FileDownloadButtonProps) {
  const handleDownload = async () => {
    if (disabled) return;

    const blob = await triggerDownload(fileId);
    const finalBlob =
      mimeType && blob.type !== mimeType
        ? new Blob([blob], { type: mimeType })
        : blob;

    const url = URL.createObjectURL(finalBlob);
    const link = document.createElement("a");

    const downloadName = fileExtension
      ? `${fileName.replace(/\.\w+$/, "")}.${fileExtension}`
      : fileName;

    link.href = url;
    link.download = downloadName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <td className="k-command-cell">
      <button
        onClick={handleDownload}
        className="file-download-link"
        disabled={disabled}
      >
        {buttonText}
      </button>
    </td>
  );
}
