export type AppConfig = {
  discoveryURL: string;
  env: string;
  logLevel: string;
  keycloakEnabled: boolean;
  applicationConfig: {
    appTimeZone: string;
    paginationConfig: {
      pageSizeOptions: number[];
      defaultSize: number;
    };
  };
  roles: Record<string, string>;

  featureFlags: {
    api: {
      GET_LATEST_TERMS: boolean;
      DOWNLOAD_TERMS_PDF: boolean;
      SAVE_TERMS: boolean;
      UPDATE_TERMS: boolean;
      GET_TEMPLATE_BY_ID: boolean;
      CREATE_TEMPLATE: boolean;
      UPDATE_TEMPLATE: boolean;
      CLIENTS_API_USE_MOCK?: boolean; // Added for clients API mock feature
    };
    ui: {
      CREATE_TEMPLATE: boolean;
      UPDATE_TEMPLATE: boolean;
    };
  };

  temporary: {
    db: string;
    authorization: string;
  };
};

const defaultAppConfig: AppConfig = {
  discoveryURL: import.meta.env.VITE_DISCOVERY_URL,
  env: import.meta.env.MODE || "development",
  logLevel: import.meta.env.VITE_LOG_LEVEL || "info",
  keycloakEnabled: import.meta.env.VITE_KEYCLOAK_ENABLED === "true",
  applicationConfig: {
    appTimeZone: "UTC",
    paginationConfig: {
      pageSizeOptions: [10, 20, 50],
      defaultSize: 10,
    },
  },
  roles: {
    system_admin: "System Admin",
    admin: "Admin",
    manager: "Manager",
    officer: "Officer",
  },
  featureFlags: {
    api: {
      GET_LATEST_TERMS: false,
      DOWNLOAD_TERMS_PDF: false,
      SAVE_TERMS: false,
      UPDATE_TERMS: false,
      GET_TEMPLATE_BY_ID: false,
      CREATE_TEMPLATE: false,
      UPDATE_TEMPLATE: false,
      CLIENTS_API_USE_MOCK: true, // Default to false, can be overridden in dev/prod configs
    },
    ui: {
      CREATE_TEMPLATE: true,
      UPDATE_TEMPLATE: true,
    },
  },
  temporary: {
    db: "NTB1",
    authorization:
      "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
  },
};

export default defaultAppConfig;
