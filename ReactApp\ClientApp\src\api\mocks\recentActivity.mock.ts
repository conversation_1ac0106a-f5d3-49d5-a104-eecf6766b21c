import type {
  RecentActivity,
  RecentActivityResponse,
} from "@/types/recentActivity";

export const mockRecentActivityData: RecentActivity[] = [
  {
    id: "1",
    setupArea: "DMS Settings",
    section: "Privileges Settings",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/05/22",
    relativeUrl: "/dashboard/privileges",
  },
  {
    id: "2",
    setupArea: "DMS Settings",
    section: "Staff Settings",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/05/22",
    relativeUrl: "/dashboard/staff",
  },
  {
    id: "3",
    setupArea: "DMS Settings",
    section: "AutoFiling Settings",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/05/22",
    relativeUrl: "/dashboard/autofill",
  },
  {
    id: "4",
    setupArea: "Portal Settings",
    section: "Terms & Conditions",
    updatedBy: "Arnold Jefferson",
    lastUpdated: "2025/01/24",
    relativeUrl: "/dashboard/terms-and-conditions",
  },
  {
    id: "5",
    setupArea: "Portal Settings",
    section: "Folder Permissions",
    updatedBy: "<PERSON>",
    lastUpdated: "2025/01/24",
    relativeUrl: "/dashboard/folder-permissions",
  },
  {
    id: "6",
    setupArea: "Shared Settings",
    section: "Client Branding",
    updatedBy: "Robert McKenzie",
    lastUpdated: "2025/01/24",
    relativeUrl: "/dashboard/branding",
  },
  {
    id: "7",
    setupArea: "Shared Settings",
    section: "Email Format Settings",
    updatedBy: "Tarquin Guthrie",
    lastUpdated: "2025/01/24",
    relativeUrl: "/dashboard/email-format",
  },
  {
    id: "8",
    setupArea: "DMS Settings",
    section: "Privileges Settings",
    updatedBy: "Nancy Drew",
    lastUpdated: "2025/03/11",
    relativeUrl: "/dashboard/privileges",
  },
  {
    id: "9",
    setupArea: "Portal Settings",
    section: "Terms & Conditions",
    updatedBy: "Samuel Kim",
    lastUpdated: "2025/04/28",
    relativeUrl: "/dashboard/terms-and-conditions",
  },
  {
    id: "10",
    setupArea: "Shared Settings",
    section: "Client Branding",
    updatedBy: "Lewis Carter",
    lastUpdated: "2025/05/03",
    relativeUrl: "/dashboard/branding",
  },
  {
    id: "11",
    setupArea: "DMS Settings",
    section: "AutoFiling Settings",
    updatedBy: "Hannah Cole",
    lastUpdated: "2025/02/14",
    relativeUrl: "/dashboard/autofill",
  },
  {
    id: "12",
    setupArea: "Portal Settings",
    section: "Folder Permissions",
    updatedBy: "Arnold Jefferson",
    lastUpdated: "2025/01/17",
    relativeUrl: "/dashboard/folder-permissions",
  },
  {
    id: "13",
    setupArea: "Shared Settings",
    section: "Email Format Settings",
    updatedBy: "Jane Robinson",
    lastUpdated: "2025/03/29",
    relativeUrl: "/dashboard/email-format",
  },
  {
    id: "14",
    setupArea: "DMS Settings",
    section: "Staff Settings",
    updatedBy: "Tarquin Guthrie",
    lastUpdated: "2025/05/04",
    relativeUrl: "/dashboard/staff",
  },
  {
    id: "15",
    setupArea: "Portal Settings",
    section: "Terms & Conditions",
    updatedBy: "Nancy Drew",
    lastUpdated: "2025/04/15",
    relativeUrl: "/dashboard/terms-and-conditions",
  },
];

export const mockRecentActivityResponse: RecentActivityResponse = {
  records: mockRecentActivityData,
  pageCount: 1,
  pageNumber: 1,
  pageSize: 20,
  totalRecordCount: mockRecentActivityData.length,
};
