import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { Security } from "@okta/okta-react";
import { OktaAuth } from "@okta/okta-auth-js";

import AppRoutes from "@/routes/appRoutes";
import AppInitializer from "@/components/AppInitializer";
import oktaConfig from "./oktaConfig";
import logger from "@/utils/logger";
import config from "./config";
import DiscoveryFEClient from "@iris/discovery.fe.client";
import { store } from "./store/store";
import { selectDiscoveryStatus } from "@/store/discoverySlice";

// Create DiscoveryClient locally since it's only used here
export const configStore = store;
export const DiscoveryClient = new DiscoveryFEClient(config.discoveryURL);

// Inline loading component
const LoadingScreen = () => (
  <div className="app-loading">Loading authentication...</div>
);

// Inline error component
const ErrorScreen = ({ error }: { error: string }) => (
  <div className="app-error">Authentication Error: {error}</div>
);

// Helper function to initialize OktaAuth with discovery-based issuer
const createOktaAuth = async (): Promise<OktaAuth> => {
  const issuer = await DiscoveryClient.getOAuthURL();
  if (!issuer) throw new Error("OAuth issuer could not be determined.");

  return new OktaAuth({
    issuer,
    clientId: oktaConfig.clientId,
    redirectUri: oktaConfig.redirectUri,
    postLogoutRedirectUri: oktaConfig.postLogoutRedirectUri,
    scopes: oktaConfig.scopes,
    tokenManager: {
      autoRenew: oktaConfig.tokenManager?.autoRenew ?? true,
      secure: oktaConfig.tokenManager?.secure ?? true,
      storageKey: oktaConfig.tokenManager?.storageKey ?? "AP_AUTH_TOKEN",
    },
  });
};

const App = () => {
  const [oktaAuth, setOktaAuth] = useState<OktaAuth | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  const discoveryStatus = useSelector(selectDiscoveryStatus);

  useEffect(() => {
    const initializeOkta = async () => {
      try {
        const auth = await createOktaAuth();
        setOktaAuth(auth);
      } catch (err) {
        if (err instanceof Error) {
          logger.error("Error initializing OktaAuth", {
            message: err.message,
            stack: err.stack,
          });
          setError(err.message);
        } else {
          logger.error("Error initializing OktaAuth", {
            error: String(err),
          });
          setError("Unknown error");
        }
      } finally {
        setLoading(false);
      }
    };

    void initializeOkta();
  }, []);

  const isLoading = loading || discoveryStatus === "loading";
  const hasError = !!error || discoveryStatus === "error";

  if (isLoading) return <LoadingScreen />;
  if (hasError) return <ErrorScreen error={error || "Discovery failed"} />;

  return (
    <Security
      oktaAuth={oktaAuth!}
      restoreOriginalUri={async (_oktaAuth, originalUri) => {
        window.location.replace(originalUri || "/dashboard");
      }}
    >
      <AppInitializer />
      <AppRoutes />
    </Security>
  );
};

const AppWithRouterAccess = () => (
  <BrowserRouter>
    <App />
  </BrowserRouter>
);

export default AppWithRouterAccess;
