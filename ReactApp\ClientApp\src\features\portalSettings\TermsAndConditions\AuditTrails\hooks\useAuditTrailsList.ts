import { useGetAuditTrailsQuery } from "@/api/auditTrailsApiSlice";
import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";
import { useMemo } from "react";
import { useGetAuditTrailsColumns } from "./useGetAuditTrailsColumns";
import { bytesToMegabytes } from "@/utils/fileSizeConverter";
import { formatDate } from "@/utils/baseGridQueryParamsBuilder";

export const useAuditTrailsList = () => {
  const { auditTrailsColumns, isLoading: isColumnsLoading } =
    useGetAuditTrailsColumns();
  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const { data, isLoading, isError, error, isFetching } =
    useGetAuditTrailsQuery({
      skip,
      take,
      filters,
      sorts,
    });

  const auditTrailsData = useMemo(() => {
    if (!data?.records) {
      return [];
    }
    return data.records?.map((trail) => ({
      ...trail,
      fileSize: bytesToMegabytes(trail?.fileSize as number) + " Mb",
      uploadedOn: formatDate(trail.uploadedOn as string),
    }));
  }, [data]);

  const totalRecordCount = useMemo(() => data?.totalRecordCount || 0, [data]);

  return {
    auditTrailsData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    isFetching,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    skip,
    take,
    filters,
    sorts,
    auditTrailsColumns,
    isColumnsLoading,
    pagination: { skip, take },
  };
};
