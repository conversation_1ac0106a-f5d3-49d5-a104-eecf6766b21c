import { useTranslation } from "react-i18next";
import { <PERSON><PERSON> } from "@progress/kendo-react-buttons";
import { DropDownList } from "@progress/kendo-react-dropdowns";
import type { DropDownListChangeEvent } from "@progress/kendo-react-dropdowns";
import AlertBox from "@/components/AlertBox/AlertBox";
import CreateTemplateTreeView from "../../templates/components/CreateTemplateTreeView";
import { mockTemplatesData } from "@/api/mocks/templatesMock";
import { useTemplateTreeView } from "../../templates/hooks/useTemplateTreeView";
import { Loader } from "@progress/kendo-react-indicators";
import "./AssignClients.scss";

interface TemplateOption {
  text: string;
  value: string;
}

interface PortalTemplatesTabProps {
  showAlert: boolean;
  selectedTemplate: string | null;
  templateOptions: TemplateOption[];
  onAlertClose: () => void;
  onTemplateChange: (_e: DropDownListChangeEvent) => void;
  onClearSelection: () => void;
  clientName?: string;
}

export default function PortalTemplatesTab({
  showAlert,
  selectedTemplate,
  templateOptions: _templateOptions, // ignore prop, use mock data
  onAlertClose,
  onTemplateChange,
  onClearSelection,
  clientName,
}: PortalTemplatesTabProps) {
  const { t } = useTranslation("dashboard");

  // Build dropdown options from mockTemplatesData
  const templateOptions: TemplateOption[] = mockTemplatesData.records.map(t => ({
    text: t.templateName,
    value: t.id,
  }));

  // Use the shared hook for folder structure and expand/collapse logic
  const {
    primaryFolders,
    validation: treeValidation,
    toggleExpand,
    isLoading: isTemplateLoading,
    isError: isTemplateError,
  } = useTemplateTreeView({
    templateId: selectedTemplate || undefined,
    isEditMode: true,
  });

  const selectedTemplateData = mockTemplatesData.records.find(t => t.id === selectedTemplate);

  return (
    <div className="portal-templates-content">
      {showAlert && (
        <AlertBox
          message={t("portalTemplates.selectTemplateMessage")}
          onClose={onAlertClose}
        />
      )}

      <div className="template-selection-section">
        <div className="template-dropdown-container">
          <label className="template-dropdown-label">
            {t("portalTemplates.templateForPublishedFiles")}:
          </label>
          <div className="template-dropdown-wrapper">
            <DropDownList
              data={templateOptions}
              textField="text"
              dataItemKey="value"
              value={templateOptions.find(opt => opt.value === selectedTemplate) || null}
              onChange={onTemplateChange}
              placeholder={t("portalTemplates.selectTemplate")}
              className="template-dropdown"
            />
            <Button
              onClick={onClearSelection}
              className="clear-selection-btn"
            >
              {t("portalTemplates.clearSelection")}
            </Button>
          </div>
        </div>
      </div>

      <div className="folder-structure-container">
        <div className="folder-structure-header">
          <h3 className="folder-structure-title">
            {t("portalTemplates.folderStructurePreview")}
          </h3>
        </div>
        <div className="folder-structure-content">
          {!selectedTemplate ? (
            <div className="no-template-message">
              {t("portalTemplates.noTemplateSelected")}
            </div>
          ) : isTemplateLoading ? (
            <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
              <Loader />
            </div>
          ) : isTemplateError ? (
            <div className="no-template-message">
              {t("portalTemplates.errorLoadingTemplate")}
            </div>
          ) : (
            <CreateTemplateTreeView
              primaryFolders={primaryFolders}
              validation={treeValidation}
              onToggleExpand={toggleExpand}
              isEditMode={true}
              readOnly={true}
            />
          )}
        </div>
      </div>
    </div>
  );
}
