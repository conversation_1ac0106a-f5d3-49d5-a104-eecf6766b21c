import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import useClientsList from "./hooks/useClientsList";
import AdvancedBaseGrid from "@/components/AdvancedBaseGrid/AdvancedBaseGrid";
import BaseGridFilterFactory from "@/components/BaseGridFilterFactory/BaseGridFilterFactory";
import GridNotice from "./components/GridNotice/GridNotice";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { AssignClientsTabStrip, ClientsFooter } from "./components";
import { useClientsAssign } from "./hooks/useClientsAssign";
import "./ClientsPage.scss";
import "./components/AssignClients.scss";

const ClientsPage = () => {
  const { t } = useTranslation("dashboard");
  const [showNotice, setShowNotice] = useState(true);
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [showGrid, setShowGrid] = useState(true);

  const {
    columns,
    isColumnsLoading,
    clientList,
    filters,
    sorts,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    handleSortChange,
    isLoading,
    isFetching,
    pagination,
    totalRecordCount,
  } = useClientsList();

  const {
    activeTab,
    showAlert,
    selectedTemplate,
    templateOptions,
    handleTabSelect,
    handleTemplateChange,
    handleClearSelection,
    handleAlertClose,
    handleFooterCancel,
    handleFooterConfirm,
  } = useClientsAssign();

  const handleCloseNotice = () => {
    setShowNotice(false);
  };

  const handleClientRowClick = (client: any) => {
    setSelectedClient(client);
    setShowGrid(false);
  };

  const handleBackToGrid = () => {
    setSelectedClient(null);
    setShowGrid(true);
  };

  const handleClientFooterCancel = () => {
    handleFooterCancel();
    handleBackToGrid();
  };

  const handleClientFooterConfirm = () => {
    handleFooterConfirm();
    // Add any additional logic for confirm action
    handleBackToGrid();
  };
  // Create a clickable cell renderer for client names
  const createClickableCell = (fieldName: string) => (props: any) => {
    const { dataItem } = props;
    return (
      <td
        style={{ cursor: 'pointer', color: '#007acc' }}
        onClick={() => handleClientRowClick(dataItem)}
      >
        {dataItem[fieldName]}
      </td>
    );
  };

  // Create data cell mapper for clickable rows
  const dataCellMapper = {
    clientName: createClickableCell('clientName'),
  };

  return (
    <SectionLayout
      footer={
        !showGrid && (
          <ClientsFooter
            onCancel={handleClientFooterCancel}
            onConfirm={handleClientFooterConfirm}
            selectedTemplate={selectedTemplate}
          />
        )
      }
    >
      <div className="client-page-content">
        {showGrid ? (
          <>
            <GridNotice show={showNotice} onClose={handleCloseNotice} />
            <AdvancedBaseGrid
              totalRecordCount={totalRecordCount}
              columns={columns}
              dataSource={clientList}
              filters={filters}
              skip={pagination.skip}
              take={pagination.take}
              onFilterChange={handleFilterChange}
              onPageChange={handlePageChange}
              onSortChange={handleSortChange}
              onRefresh={handleRefresh}
              isLoading={isLoading || isFetching}
              sorts={sorts}
              isColumnsLoading={isColumnsLoading}
              dataCellMapper={dataCellMapper}
              renderFilterFactory={(props, column) => (
                <BaseGridFilterFactory
                  {...props}
                  column={column}
                  useAutoSuggestHook={() => ({
                    fetchSuggestions: async (_field: string, _value: string) => [],
                    isLoading: false,
                  })}
                />
              )}
            />
          </>
        ) : (
          <AssignClientsTabStrip
            activeTab={activeTab}
            showAlert={showAlert}
            selectedTemplate={selectedTemplate}
            templateOptions={templateOptions}
            handleTabSelect={handleTabSelect}
            handleTemplateChange={handleTemplateChange}
            handleClearSelection={handleClearSelection}
            handleAlertClose={handleAlertClose}
            clientName={selectedClient?.clientName}
          />
        )}
      </div>
    </SectionLayout>
  );
};

export default ClientsPage;
