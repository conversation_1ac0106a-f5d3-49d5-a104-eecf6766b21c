import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import useClientsList from "./hooks/useClientsList";
import AdvancedBaseGrid from "@/components/AdvancedBaseGrid/AdvancedBaseGrid";
import BaseGridFilterFactory from "@/components/BaseGridFilterFactory/BaseGridFilterFactory";
import GridNotice from "./components/GridNotice/GridNotice";
import { useState } from "react";
import "./ClientsPage.scss";

const ClientsPage = () => {
  const [showNotice, setShowNotice] = useState(true);
  const {
    columns,
    isColumnsLoading,
    clientList,
    filters,
    sorts,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    handleSortChange,
    isLoading,
    isFetching,
    pagination,
    totalRecordCount,
  } = useClientsList();
  const handleCloseNotice = () => {
    setShowNotice(false);
  };
  return (
    <SectionLayout>
      <div className="client-page-content">
        <GridNotice show={showNotice} onClose={handleCloseNotice} />
        <AdvancedBaseGrid
          totalRecordCount={totalRecordCount}
          columns={columns}
          dataSource={clientList}
          filters={filters}
          skip={pagination.skip}
          take={pagination.take}
          onFilterChange={handleFilterChange}
          onPageChange={handlePageChange}
          onSortChange={handleSortChange}
          onRefresh={handleRefresh}
          isLoading={isLoading || isFetching}
          sorts={sorts}
          isColumnsLoading={isColumnsLoading}
          renderFilterFactory={(props, column) => (
            <BaseGridFilterFactory
              {...props}
              column={column}
              useAutoSuggestHook={() => ({
                fetchSuggestions: async (_field: string, _value: string) => [],
                isLoading: false,
              })}
            />
          )}
        />
      </div>
    </SectionLayout>
  );
};

export default ClientsPage;
