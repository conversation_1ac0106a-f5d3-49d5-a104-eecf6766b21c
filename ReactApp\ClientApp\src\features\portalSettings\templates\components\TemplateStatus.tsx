import { Label } from "@progress/kendo-react-labels";
import { CheckCircleIcon, NoSymbolIcon } from "@heroicons/react/24/outline";
import { TEMPLATE_STATUS } from "../constants/status";

const statusConfig: Record<
  "Active" | "Inactive",
  { icon: React.ElementType; color: string; label: string }
> = {
  Active: {
    icon: CheckCircleIcon,
    color: "green",
    label: TEMPLATE_STATUS.ACTIVE,
  },
  Inactive: {
    icon: NoSymbolIcon,
    color: "red",
    label: TEMPLATE_STATUS.INACTIVE,
  },
};

export const TemplateStatus = (value: string) => {
  const statusKey =
    value === "true" ? "Active" : value === "false" ? "Inactive" : value;

  const config = statusConfig[statusKey as keyof typeof statusConfig];

  if (!config) return <td>{value}</td>;

  const Icon = config.icon;

  return (
    <td style={{ display: "flex", alignItems: "center", gap: 6 }}>
      <Icon style={{ width: 18, height: 18, color: config.color }} />
      <Label style={{ color: config.color }}>{config.label}</Label>
    </td>
  );
};
