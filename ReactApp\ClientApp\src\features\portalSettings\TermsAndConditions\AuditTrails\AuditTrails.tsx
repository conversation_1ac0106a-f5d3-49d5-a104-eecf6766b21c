import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { ExportCsvFile } from "./components";
import AdvancedBaseGrid from "@/components/AdvancedBaseGrid/AdvancedBaseGrid";
import { useAuditTrailsAutoSuggest } from "./hooks/useAuditTrailsAutoSuggest";
import BaseGridFilterFactory from "@/components/BaseGridFilterFactory/BaseGridFilterFactory";
import "./AuditTrails.scss";
import { useAuditTrailsList } from "./hooks/useAuditTrailsList";

export default function AuditTrails() {
  const {
    auditTrailsData,
    totalRecordCount,
    auditTrailsColumns,
    isLoading,
    isColumnsLoading,
    pagination,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    isFetching,
  } = useAuditTrailsList();

  return (
    <SectionLayout>
      <AdvancedBaseGrid
        totalRecordCount={totalRecordCount}
        columns={auditTrailsColumns}
        dataSource={auditTrailsData}
        filters={filters}
        skip={pagination.skip}
        take={pagination.take}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
        onRefresh={handleRefresh}
        isLoading={isLoading || isFetching}
        sorts={sorts}
        isColumnsLoading={isColumnsLoading}
        actionsColumn={{
          label: "Acceptance History",
          renderer: (props) => <ExportCsvFile {...props} />,
        }}
        renderFilterFactory={(props, column) => (
          <BaseGridFilterFactory
            {...props}
            column={column}
            useAutoSuggestHook={useAuditTrailsAutoSuggest}
            dataRangeMode
          />
        )}
      />
    </SectionLayout>
  );
}
