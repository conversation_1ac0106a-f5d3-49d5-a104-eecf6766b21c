.customPagerContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
}

.customPagerContainer > div {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.refreshIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.refreshIcon.spin {
  animation: spin 1s linear infinite;
  pointer-events: none; // optional: prevent interaction
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
