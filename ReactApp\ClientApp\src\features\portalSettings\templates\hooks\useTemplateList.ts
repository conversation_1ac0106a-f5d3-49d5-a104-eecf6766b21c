import { useGetTemplateListQuery } from "@/api/templatesApiSlice";
import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";
import { formatDate } from "@/utils/baseGridQueryParamsBuilder";
import { useMemo, useState } from "react";
import { useTemplateGridColumns } from "./useTemplateGridColumns";

export const useTemplateList = () => {
  const [selectedTemplateId, setSelectedTemplateId] = useState<
    string | undefined
  >(undefined);

  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const { columns, isColumnsLoading } = useTemplateGridColumns();

  const { data, isLoading, isError, error, refetch, isFetching } =
    useGetTemplateListQuery({
      skip,
      take,
      filters,
      sorts,
    });

  const templateList = useMemo(() => data?.records || [], [data]);
  const totalRecordCount = useMemo(() => data?.totalRecordCount || 0, [data]);

  const templateListData = useMemo(() => {
    if (!templateList) {
      return [];
    }
    return templateList.map((temp) => ({
      ...temp,
      createdOn: formatDate(temp.createdOn as string),
    }));
  }, [templateList]);

  return {
    templateListData,
    totalRecordCount,
    isLoading,
    isError,
    error,
    refetch,
    isFetching,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    selectedTemplateId,
    setSelectedTemplateId,
    pagination: { skip, take },
    filters,
    sorts,
    columns,
    isColumnsLoading,
  };
};
