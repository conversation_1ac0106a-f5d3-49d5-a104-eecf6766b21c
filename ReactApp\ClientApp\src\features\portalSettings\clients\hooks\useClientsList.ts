import { useGetClientListQuery } from "@/api/clientsApiSlice";
import { useClientsGridColumns } from "./useClientsGridColumns";
import { useMemo } from "react";
import { useAdvancedBaseGridController } from "@/hooks/useAdvancedBaseGridController";

const useClientsList = () => {
  const { columns, isColumnsLoading } = useClientsGridColumns();

  const {
    skip,
    take,
    filters,
    sorts,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
  } = useAdvancedBaseGridController();

  const { data, isLoading, refetch, isFetching } = useGetClientListQuery({
    skip,
    take,
    filters,
    sorts,
  });

  const clientList = useMemo(() => data?.records || [], [data]);
  const totalRecordCount = useMemo(() => data?.totalRecordCount || 0, [data]);

  return {
    clientList,
    totalRecordCount,
    columns,
    isColumnsLoading,
    isLoading,
    refetch,
    isFetching,
    handlePageChange,
    handleFilterChange,
    handleSortChange,
    handleRefresh,
    filters,
    sorts,
    pagination: { skip, take },
  };
};

export default useClientsList;
