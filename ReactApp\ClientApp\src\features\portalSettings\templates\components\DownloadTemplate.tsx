import { useLazyDownloadTemplateQuery } from "@/api/templatesApiSlice";
import type { GridCustomCellProps } from "@progress/kendo-react-grid";
import FileDownloadButton from "@/components/FileDownloadButton/FileDownloadButton";

export default function DownloadCell({ dataItem }: GridCustomCellProps) {
  const [triggerDownload] = useLazyDownloadTemplateQuery();

  return (
    <FileDownloadButton
      fileId={String(dataItem.templateId)}
      triggerDownload={(templateId) =>
        triggerDownload(String(templateId)).unwrap()
      }
      fileName={`${"Template"}_${dataItem.templateId}_${Date.now()}.csv`}
      disabled={!dataItem?.assignedClientsCsvUrl}
      buttonText="Export to CSV"
      mimeType="text/csv"
      fileExtension="csv"
    />
  );
}
