import { useState, useMemo, useEffect } from "react";
import { debounce } from "lodash";
import { useNavigate } from "react-router-dom";
import { useGetLatest } from "./useGetLatest";
import { useSaveTerms } from "./useSaveTerms";
import { useUpdateTerms } from "./useUpdateTerms";
import type { Terms } from "@/types/terms";
import logger from "@/utils/logger";
import { fetchTermsPdfBlob } from "../utils/fetchTermsPdfBlob";
import { TRIGGER_KEYS } from "../constants";

export function useTermsAndConditionsController() {
  const { terms, isLoading, isFetching, refetch } = useGetLatest();
  const { save, isSaving } = useSaveTerms();
  const { update, isUpdating } = useUpdateTerms();
  const navigate = useNavigate();

  const [previewStatement, setPreviewStatement] = useState<string | null>(null);
  const [defaultPreviewStatement, setDefaultPreviewStatement] =
    useState<string>("");
  const [previewBlobUrl, setPreviewBlobUrl] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState("");
  const [uploadError, setUploadError] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState("");
  const [showAlert, setShowAlert] = useState(true);
  const [uploadedFileName, setUploadedFileName] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [termsData, setTermsData] = useState<Terms | null>(null);
  const [remoteBlobUrl, setRemoteBlobUrl] = useState<string | null>(null);
  const [statementInput, setStatementInput] = useState<string>("");

  const [triggerPoints, setTriggerPoints] = useState({
    [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]: false,
    [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: false,
    [TRIGGER_KEYS.PROMPT_ANNUALLY]: false,
    [TRIGGER_KEYS.PROMPT_QUARTERLY]: false,
  });

  const handleUpload = useMemo(
    () =>
      debounce((event: any) => {
        const fileInfo = event.affectedFiles?.[0];
        const file = fileInfo?.getRawFile?.() || fileInfo?.rawFile;

        // Always show attempted file name
        if (fileInfo?.name) {
          setUploadedFileName(fileInfo.name);
        }

        const hasErrors = fileInfo?.validationErrors;
        if (hasErrors?.length) {
          logger.warn("File validation failed", hasErrors);
          setUploadError(hasErrors);
          return;
        }

        setUploadError("");

        if (file instanceof File) {
          setSelectedFile(file);
        } else {
          logger.error("Uploaded item is not a valid File object", fileInfo);
          setErrorMessage("Failed to read uploaded file. Please try again.");
        }
      }, 300),
    [],
  );

  useEffect(() => {
    if (terms?.triggerPoints) {
      setTriggerPoints({
        [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]:
          !!terms.triggerPoints.promptAfterFirstLogin,
        [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]:
          !!terms.triggerPoints.promptWhenUpdated,
        [TRIGGER_KEYS.PROMPT_ANNUALLY]: !!terms.triggerPoints.promptAnnually,
        [TRIGGER_KEYS.PROMPT_QUARTERLY]: !!terms.triggerPoints.promptQuarterly,
      });
    }

    if (terms?.statementOfAgreement) {
      setStatementInput(terms.statementOfAgreement);
      setDefaultPreviewStatement(terms.statementOfAgreement);
    }
  }, [terms]);

  useEffect(() => {
    if (terms) {
      setTermsData(terms);
    }
  }, [terms]);

  useEffect(() => {
    let blobUrl: string | null = null;

    const loadPdf = async () => {
      if (terms?.termsAndConditionsId && !selectedFile) {
        try {
          const blob = await fetchTermsPdfBlob(terms.termsAndConditionsId);
          blobUrl = URL.createObjectURL(blob);
          setRemoteBlobUrl(blobUrl);
        } catch (err: unknown) {
          const error = err instanceof Error ? err : new Error(String(err));
          logger.error("Failed to download PDF blob", {
            message: error.message,
            stack: error.stack,
          });
        }
      } else {
        logger.warn(
          "Skipped PDF fetch. Either no termsId or selectedFile exists.",
        );
      }
    };

    loadPdf();

    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [terms, selectedFile]);

  useEffect(() => {
    return () => {
      handleUpload.cancel();
    };
  }, [handleUpload]);

  useEffect(() => {
    return () => {
      if (previewBlobUrl) {
        URL.revokeObjectURL(previewBlobUrl);
      }
    };
  }, [previewBlobUrl]);

  const handleRemoveFile = () => {
    setUploadedFileName("");
    setSelectedFile(null);
    setUploadError("");
  };

  const handleSaveTerms = async () => {
    const currentStatement = statementInput.trim();
    const originalStatement = terms?.statementOfAgreement?.trim() || "";

    const payload = {
      termsAndConditionsId: terms?.termsAndConditionsId ?? 0,
      statementOfAgreement: currentStatement,
      triggerPoints: {
        [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]:
          triggerPoints.promptAfterFirstLogin,
        [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: triggerPoints.promptWhenUpdated,
        [TRIGGER_KEYS.PROMPT_ANNUALLY]: triggerPoints.promptAnnually,
        [TRIGGER_KEYS.PROMPT_QUARTERLY]: triggerPoints.promptQuarterly,
      },
    };

    const hasChanges =
      currentStatement !== originalStatement ||
      JSON.stringify(payload.triggerPoints) !==
        JSON.stringify({
          [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]:
            terms?.triggerPoints.promptAfterFirstLogin,
          [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]:
            terms?.triggerPoints.promptWhenUpdated,
          [TRIGGER_KEYS.PROMPT_ANNUALLY]: terms?.triggerPoints.promptAnnually,
          [TRIGGER_KEYS.PROMPT_QUARTERLY]: terms?.triggerPoints.promptQuarterly,
        });

    try {
      if (previewBlobUrl && selectedFile instanceof File) {
        await save(
          {
            ...payload,
            file: selectedFile,
          },
          (message) => setErrorMessage(message),
          async () => {
            setSuccessMessage("Terms saved successfully.");
            await resetForm();
          },
        );
      } else if (hasChanges) {
        await update(
          payload,
          (message) => setErrorMessage(message),
          async (msg) => {
            setSuccessMessage(msg || "Terms updated successfully.");
            await resetForm();
          },
        );
      } else {
        setErrorMessage("No changes to save.");
      }
    } catch (err) {
      logger.error("Save/update failed", err as any);
      setErrorMessage("An error occurred while saving.");
    }
  };

  const handleGeneratePreview = async () => {
    const originalStatement = terms?.statementOfAgreement?.trim() || "";
    const currentStatement = statementInput.trim();

    if (!selectedFile && !termsData && originalStatement === currentStatement) {
      setErrorMessage("Missing file or no changes detected");
      return;
    }

    logger.info("Generating preview with:", {
      file: selectedFile,
      statementOfAgreement: currentStatement,
      triggerPoints,
    });

    if (selectedFile && selectedFile instanceof Blob) {
      const blobUrl = URL.createObjectURL(selectedFile);
      setPreviewBlobUrl(blobUrl);
    }

    setPreviewStatement(currentStatement);
    setShowAlert(true);
    setErrorMessage("");
  };

  const isGenerateDisabled =
    (!selectedFile &&
      statementInput.trim() === (terms?.statementOfAgreement?.trim() || "")) ||
    !!uploadError;

  const resetForm = async () => {
    try {
      const { data } = await refetch();
      if (data) {
        setStatementInput(data.statementOfAgreement || "");
        setDefaultPreviewStatement(data.statementOfAgreement || "");
        setTriggerPoints({
          promptAfterFirstLogin: !!data.triggerPoints.promptAfterFirstLogin,
          promptWhenUpdated: !!data.triggerPoints.promptWhenUpdated,
          promptAnnually: !!data.triggerPoints.promptAnnually,
          promptQuarterly: !!data.triggerPoints.promptQuarterly,
        });
        setTermsData(data);
        setErrorMessage("");
        setUploadError("");
        setUploadedFileName("");
        setSelectedFile(null);
        setPreviewBlobUrl(null);
        setPreviewStatement(null);
      }
    } catch (error) {
      logger.error("Reset failed", error as any);
      setErrorMessage("Failed to reset form.");
    }
  };

  const hasUnsavedChanges = () => {
    if (!terms) return false;

    const hasStatementChanged =
      statementInput.trim() !== (terms.statementOfAgreement?.trim() ?? "");

    const hasTriggerPointsChanged =
      terms.triggerPoints?.promptAfterFirstLogin !==
        triggerPoints.promptAfterFirstLogin ||
      terms.triggerPoints?.promptAnnually !== triggerPoints.promptAnnually ||
      terms.triggerPoints?.promptQuarterly !== triggerPoints.promptQuarterly ||
      terms.triggerPoints?.promptWhenUpdated !==
        triggerPoints.promptWhenUpdated;

    const hasFileChanged = selectedFile !== null;

    return hasStatementChanged || hasTriggerPointsChanged || hasFileChanged;
  };

  return {
    terms,
    isLoading,
    isFetching,
    isSaving: isSaving || isUpdating,
    errorMessage,
    setErrorMessage,
    uploadError,
    setUploadError,
    successMessage,
    setSuccessMessage,
    showAlert,
    setShowAlert,
    uploadedFileName,
    setUploadedFileName,
    selectedFile,
    setSelectedFile,
    triggerPoints,
    setTriggerPoints,
    handleUpload,
    handleRemoveFile,
    handleSaveTerms,
    handleGeneratePreview,
    resetForm,
    navigate,
    termsData,
    setTermsData,
    remoteBlobUrl,
    previewStatement,
    defaultPreviewStatement,
    previewBlobUrl,
    statementInput,
    setStatementInput,
    isGenerateDisabled,
    hasUnsavedChanges,
  };
}
