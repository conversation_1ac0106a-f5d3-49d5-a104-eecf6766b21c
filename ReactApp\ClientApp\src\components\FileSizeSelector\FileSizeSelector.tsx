import {
  NumericTextBox,
  type NumericTextBoxChangeEvent,
} from "@progress/kendo-react-inputs";
import { Button } from "@progress/kendo-react-buttons";
import { filterClearIcon } from "@progress/kendo-svg-icons";
import { useState } from "react";
import "./FileSizeSelector.scss";

interface FileSizeSelectorProps {
  value?: { start?: number; end?: number } | null;
  onChange: (_event: {
    value: any;
    operator: string;
    syntheticEvent: any;
  }) => void;
  dataRangeMode?: boolean;
}

const FileSizeSelector = ({
  value = null,
  onChange,
  dataRangeMode = false,
}: FileSizeSelectorProps) => {
  const [min, setMin] = useState<number | null>(
    dataRangeMode && value?.start != null
      ? value.start / 1048576
      : (value?.start ?? null),
  );
  const [max, setMax] = useState<number | null>(
    dataRangeMode && value?.end != null
      ? value.end / 1048576
      : (value?.end ?? null),
  );

  const toBytes = (val: number | null) =>
    dataRangeMode && val != null ? val * 1048576 : val;

  const triggerChangeIfAnyValueExists = (
    minVal: number | null,
    maxVal: number | null,
    event: any,
  ) => {
    if (minVal != null || maxVal != null) {
      onChange({
        value: {
          start: toBytes(minVal),
          end: toBytes(maxVal),
        },
        operator: "intValueRange",
        syntheticEvent: event,
      });
    }
  };

  const handleMinChange = (event: NumericTextBoxChangeEvent) => {
    const val = event.value as number | null;
    setMin(val);
    triggerChangeIfAnyValueExists(val, max, event.syntheticEvent);
  };

  const handleMaxChange = (event: NumericTextBoxChangeEvent) => {
    const val = event.value as number | null;
    setMax(val);
    triggerChangeIfAnyValueExists(min, val, event.syntheticEvent);
  };

  const handleClear = (event: any) => {
    event.preventDefault();
    setMin(null);
    setMax(null);
    onChange({
      value: null,
      operator: "",
      syntheticEvent: event,
    });
  };

  return (
    <div className="gridCellFilterContainer">
      <NumericTextBox
        value={min}
        placeholder="Min (MB)"
        onChange={handleMinChange}
        min={0}
        width={75}
      />
      <NumericTextBox
        value={max}
        placeholder="Max (MB)"
        onChange={handleMaxChange}
        min={0}
        width={75}
      />
      <Button
        svgIcon={filterClearIcon}
        title="Clear"
        disabled={min == null && max == null}
        onClick={handleClear}
        type="button"
      />
    </div>
  );
};

export default FileSizeSelector;
