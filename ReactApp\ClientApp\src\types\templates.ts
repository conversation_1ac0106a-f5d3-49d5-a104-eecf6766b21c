export interface Template {
  templateId: string;
  templateName: string;
  createdBy: string;
  createdOn: string;
  description: string;
  status: string;
  assignedClients: string[];
}

export interface TemplateListResponse {
  records: Template[];
  pageCount: number;
  pageNumber: number;
  pageSize: number;
  totalRecordCount: number;
}

export interface TemplateNode {
  id: number;
  name: string;
  childNodes: TemplateChildNode[];
}

export interface TemplateChildNode {
  id: number;
  name: string;
}

export interface TemplateDetailResponse {
  templateName: string;
  templateDescription: string;
  active: boolean;
  nodes: TemplateNode[];
}

export interface CreateTemplateRequest {
  templateName: string;
  templateDescription: string;
  parentNodes: CreateTemplateParentNode[];
  active: boolean;
}

export interface CreateTemplateParentNode {
  name: string;
  childNodes?: CreateTemplateChildNode[];
}

export interface CreateTemplateChildNode {
  name: string;
}

export interface CreateTemplateResponse {
  id: string;
  templateName: string;
  templateDescription: string;
  parentNodes: CreateTemplateParentNode[];
  active: boolean;
  success: boolean;
}

export interface CreateTemplateFormData {
  templateName: string;
  templateDescription: string;
  active: boolean;
}

export interface CreateTemplateValidation {
  nameError: string;
}

export interface SecondaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
}

export interface PrimaryFolder {
  id: string;
  name: string;
  isEditing?: boolean;
  expanded?: boolean;
  secondaryFolders: SecondaryFolder[];
}

export interface TreeValidation {
  duplicateNameError: string;
  deleteLastSecondaryError: string;
}
export interface UpdateTemplateRequest {
  templateId: string;
  templateName: string;
  templateDescription: string;
  parentNodes: UpdateTemplateParentNode[];
  active: boolean;
}

export interface UpdateTemplateParentNode {
  id?: number | null;
  name: string;
  childNodes?: UpdateTemplateChildNode[];
}

export interface UpdateTemplateChildNode {
  id?: number | null;
  name: string;
}

export interface UpdateTemplateResponse {
  templateId: string;
  templateName: string;
  templateDescription: string;
  parentNodes: UpdateTemplateParentNode[];
  active: boolean;
  success: boolean;
}
