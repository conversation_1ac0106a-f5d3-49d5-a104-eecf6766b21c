import { Button } from "@progress/kendo-react-buttons";
import {
  chevronRightIcon,
  chevronDoubleLeftIcon,
  chevronDoubleRightIcon,
  chevronLeftIcon,
} from "@progress/kendo-svg-icons";
import "./GridPagination.scss";
import { ArrowPathIcon } from "@heroicons/react/24/outline";

interface GridPaginationProps {
  skip: number;
  take: number;
  total: number;
  onPageChange: (_event: { page: { skip: number; take: number } }) => void;
  onRefresh: () => void;
  loading?: boolean;
}

export default function GridPagination({
  skip,
  take,
  total,
  onPageChange,
  onRefresh,
  loading = false,
}: GridPaginationProps) {
  const totalPages = Math.ceil(total / take);
  const currentPage = Math.floor(skip / take) + 1;
  const isFirstPage = currentPage === 1;
  const isLastPage = currentPage === totalPages || totalPages === 0;

  const goToPage = (newSkip: number) => {
    onPageChange({ page: { skip: newSkip, take } });
  };

  const renderPageInfo = () => {
    if (total === 0) return "No items";
    const start = skip + 1;
    const end = Math.min(skip + take, total);
    return `${start} - ${end} of ${total} items`;
  };

  if (total == 0) return null;

  return (
    <div className="customPagerContainer">
      <div>
        <span className="kendo-pager-info">
          Page {currentPage} of {totalPages || 1}
        </span>
        <Button
          disabled={isFirstPage}
          onClick={() => goToPage(0)}
          themeColor="light"
          fillMode="flat"
          svgIcon={chevronDoubleLeftIcon}
        />
        <Button
          disabled={isFirstPage}
          onClick={() => goToPage(skip - take)}
          themeColor="light"
          fillMode="flat"
          svgIcon={chevronLeftIcon}
        />
        <Button
          disabled={isLastPage}
          onClick={() => goToPage(skip + take)}
          themeColor="light"
          fillMode="flat"
          svgIcon={chevronRightIcon}
        />
        <Button
          disabled={isLastPage}
          onClick={() => goToPage((totalPages - 1) * take)}
          themeColor="light"
          fillMode="flat"
          svgIcon={chevronDoubleRightIcon}
        />
      </div>
      <div>
        <span className="kendo-pager-info">{renderPageInfo()}</span>
        <ArrowPathIcon
          className={`refreshIcon ${loading ? "spin" : ""}`}
          onClick={!loading ? onRefresh : undefined}
          title="Refresh Data"
        />
      </div>
    </div>
  );
}
