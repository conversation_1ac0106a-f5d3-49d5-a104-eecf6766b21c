import { Card } from "@progress/kendo-react-layout";
import type { ReactNode } from "react";

interface FormCardProps {
  title?: string;
  children: ReactNode;
  className?: string;
}

export default function FormCard({
  title,
  children,
  className = "",
}: FormCardProps) {
  return (
    <Card className={className}>
      {title && <h3 className="section-header">{title}</h3>}
      <div className="section-inner">{children}</div>
    </Card>
  );
}
