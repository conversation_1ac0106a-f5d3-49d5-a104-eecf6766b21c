import { createApi } from "@reduxjs/toolkit/query/react";
import type {
  FetchBaseQueryError,
  QueryReturnValue,
} from "@reduxjs/toolkit/query";
import type { Terms } from "@/types/terms";
import config from "@/config";
import { baseQueryWithReauth } from "./interceptorsSlice";
import { mockTermsData } from "./mocks/terms.mock";
// import { OperationalServiceTypes } from "@iris/discovery.fe.client";//update here after creating a new OperationalServiceTypes

const wait = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export const termsApiSlice = createApi({
  reducerPath: "termsApi",
  baseQuery: baseQueryWithReauth,
  endpoints: (builder) => ({
    getLatest: builder.query<Terms, void>({
      queryFn: async (_arg, _api, _extra, baseQuery) => {
        if (config.featureFlags.api.GET_LATEST_TERMS) {
          await wait(1000);
          return { data: mockTermsData, meta: {} };
        }

        const result = await baseQuery({
          url: `api/terms-and-conditions/latest`,
          method: "GET",
          headers: {
            Accept: "text/plain",
          },
          // meta: OperationalServiceTypes.FileManagementService, //update here after creating a new OperationalServiceTypes
          meta: 99,
        });

        return result as QueryReturnValue<Terms, FetchBaseQueryError, {}>;
      },
    }),

    saveTerms: builder.mutation<void, Partial<Terms> & { file?: File }>({
      queryFn: async (body, _api, _extra, baseQuery) => {
        if (config.featureFlags.api.SAVE_TERMS) {
          await wait(1000);
          return { data: undefined, meta: {} };
        }

        const formData = new FormData();

        if (body?.file instanceof File && body.file.size > 0) {
          formData.append("File", body.file);
        }

        if (body?.statementOfAgreement?.trim()) {
          formData.append(
            "StatementOfAgreement",
            body.statementOfAgreement.trim(),
          );
        }

        const tp = body?.triggerPoints;
        if (tp) {
          formData.append(
            "TriggerPoints.PromptAfterFirstLogin",
            String(tp.promptAfterFirstLogin),
          );
          formData.append(
            "TriggerPoints.PromptAnnually",
            String(tp.promptAnnually),
          );
          formData.append(
            "TriggerPoints.PromptQuarterly",
            String(tp.promptQuarterly),
          );
          formData.append(
            "TriggerPoints.PromptWhenUpdated",
            String(tp.promptWhenUpdated),
          );
        }

        const result = await baseQuery({
          url: `api/terms-and-conditions/upload`,
          method: "POST",
          body: formData,
        });

        return result as QueryReturnValue<void, FetchBaseQueryError, {}>;
      },
    }),

    updateTerms: builder.mutation<
      { success: boolean; message: string },
      {
        termsAndConditionsId: number;
        statementOfAgreement: string;
        triggerPoints: {
          promptAfterFirstLogin: boolean;
          promptAnnually: boolean;
          promptQuarterly: boolean;
          promptWhenUpdated: boolean;
        };
      }
    >({
      queryFn: async (body, _api, _extra, baseQuery) => {
        if (config.featureFlags.api.UPDATE_TERMS) {
          await wait(1000);
          return {
            data: {
              success: true,
              message: "Terms & Conditions updated successfully.",
            },
            meta: {},
          };
        }

        const { termsAndConditionsId, ...rest } = body;

        const result = await baseQuery({
          url: `api/terms-and-conditions/${termsAndConditionsId}`,
          method: "PUT",
          headers: {
            Accept: "*/*",
            "Content-Type": "application/json-patch+json",
          },
          body: rest,
        });

        return result as QueryReturnValue<
          { success: boolean; message: string },
          FetchBaseQueryError,
          {}
        >;
      },
    }),
  }),
});

export const {
  useGetLatestQuery,
  useSaveTermsMutation,
  useUpdateTermsMutation,
} = termsApiSlice;
