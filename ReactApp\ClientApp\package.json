{"name": "portal-manager-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "tsc -b && vite build --mode production", "lint": "eslint .", "preview": "vite preview", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "prepare": "husky install", "test": "vitest run", "typecheck": "tsc --project tsconfig.app.json --noEmit"}, "dependencies": {"@heroicons/react": "^2.2.0", "@iris/discovery.fe.client": "^2.0.1", "@okta/okta-auth-js": "^7.12.1", "@okta/okta-react": "^6.10.0", "@progress/kendo-font-icons": "^4.3.0", "@progress/kendo-licensing": "1.6.0", "@progress/kendo-react-buttons": "11.2.0", "@progress/kendo-react-common": "11.2.0", "@progress/kendo-react-dialogs": "^11.2.0", "@progress/kendo-react-grid": "11.2.0", "@progress/kendo-react-inputs": "11.2.0", "@progress/kendo-react-intl": "11.2.0", "@progress/kendo-react-layout": "11.2.0", "@progress/kendo-react-notification": "^11.2.0", "@progress/kendo-react-pdf-viewer": "^11.2.0", "@progress/kendo-react-treeview": "^11.2.0", "@progress/kendo-react-upload": "11.2.0", "@progress/kendo-svg-icons": "^4.3.0", "@progress/kendo-theme-default": "^11.0.2", "@reduxjs/toolkit": "^2.8.2", "dayjs": "^1.11.13", "i18next": "^25.1.3", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.5.1", "react-redux": "^9.2.0", "react-router-dom": "^6.30.0", "use-debounce": "^10.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@progress/kendo-theme-utils": "^11.0.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/classnames": "^2.3.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-i18next": "^7.8.3", "@types/react-redux": "^7.1.34", "@typescript-eslint/parser": "^8.34.0", "@vitejs/plugin-react": "^4.4.1", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "^3.2.3", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "sass": "^1.89.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix --max-warnings=0", "prettier --write"]}}