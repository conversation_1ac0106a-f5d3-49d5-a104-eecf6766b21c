import { useCallback } from "react";
import { useLazyGetTemplatesSearchOptionsQuery } from "@/api/templatesApiSlice";

export const useTemplatesAutoSuggest = () => {
  const [trigger, { isLoading }] = useLazyGetTemplatesSearchOptionsQuery();

  const fetchSuggestions = useCallback(
    async (field: string, value: string): Promise<string[]> => {
      if (!value.trim()) return [];
      try {
        const response = await trigger({ field, value }).unwrap();
        return response || [];
      } catch {
        return [];
      }
    },
    [trigger],
  );

  return {
    fetchSuggestions,
    isLoading,
  };
};
