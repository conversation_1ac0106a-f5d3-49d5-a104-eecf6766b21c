import { useState } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@progress/kendo-react-buttons";
import { Dialog, DialogActionsBar } from "@progress/kendo-react-dialogs";
import { GridLayout, GridLayoutItem } from "@progress/kendo-react-layout";

import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import AlertBox from "@/components/AlertBox/AlertBox";
import FormCard from "@/components/FormCard/FormCard";

import UploadSection from "./components/UploadSection";
import ConditionsForm from "./components/ConditionsForm";
import StatementEditor from "./components/StatementEditor";
import PDFPreviewPanel from "./components/PDFPreviewPanel";

import { useTermsAndConditionsController } from "./hooks/useTermsAndConditionsController";

import "./TermsAndConditions.scss";
import { ROUTES } from "@/constants/routes";

export default function TermsAndConditionsPage() {
  const { t } = useTranslation("dashboard");
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  const {
    terms,
    isLoading,
    isFetching,
    isSaving,
    errorMessage,
    setErrorMessage,
    successMessage,
    setSuccessMessage,
    showAlert,
    setShowAlert,
    uploadedFileName,
    selectedFile,
    triggerPoints,
    setTriggerPoints,
    handleUpload,
    handleRemoveFile,
    handleSaveTerms,
    handleGeneratePreview,
    resetForm,
    navigate,
    remoteBlobUrl,
    previewStatement,
    previewBlobUrl,
    statementInput,
    setStatementInput,
    isGenerateDisabled,
    hasUnsavedChanges,
    defaultPreviewStatement,
  } = useTermsAndConditionsController();

  const handleCancelClick = () => {
    if (hasUnsavedChanges()) {
      setShowCancelDialog(true);
    } else {
      setShowCancelDialog(false);
    }
  };

  const handleConfirmCancel = () => {
    setShowCancelDialog(false);
    resetForm();
  };

  return (
    <>
      {/* Cancel Confirmation Dialog */}
      {showCancelDialog && (
        <Dialog onClose={() => setShowCancelDialog(false)}>
          <p>{t("cancel")}</p>
          <DialogActionsBar>
            <Button themeColor="primary" onClick={handleConfirmCancel}>
              {t("yes")}
            </Button>
            <Button onClick={() => setShowCancelDialog(false)}>
              {t("no")}
            </Button>
          </DialogActionsBar>
        </Dialog>
      )}

      <SectionLayout
        isLoading={isLoading}
        isFetching={isFetching}
        isSaving={isSaving}
        errorMessage={errorMessage}
        onCloseError={() => setErrorMessage("")}
        successMessage={successMessage}
        onCloseSuccess={() => setSuccessMessage("")}
        headerActions={
          <>
            <Button
              size="small"
              className="header-action-btn"
              icon="file"
              themeColor="base"
              onClick={handleGeneratePreview}
              disabled={isGenerateDisabled}
            >
              {t("generate_preview")}
            </Button>

            <Button
              size="small"
              className="header-action-btn"
              icon="check"
              themeColor="base"
              onClick={() => navigate(ROUTES.AUDIT_TRAILS)}
            >
              {t("audit_trails")}
            </Button>
          </>
        }
        footer={
          <>
            <Button
              className="cancel-link"
              themeColor="base"
              fillMode="flat"
              onClick={handleCancelClick}
              disabled={!hasUnsavedChanges()}
            >
              {t("cancel")}
            </Button>

            <Button
              themeColor="base"
              disabled={isSaving}
              onClick={handleSaveTerms}
            >
              {isSaving ? t("saving") : t("confirm")}
            </Button>
          </>
        }
      >
        <GridLayout
          cols={[{ width: "1fr" }, { width: "1fr" }]}
          gap={{ rows: 16, cols: 24 }}
          style={{ width: "100%" }}
        >
          <GridLayoutItem>
            <FormCard title={t("upload_title")} className="content-left">
              {showAlert && (
                <AlertBox
                  message={t("upload_helper_text")}
                  onClose={() => setShowAlert(false)}
                />
              )}

              <FormCard className="upload-wrapper">
                <UploadSection
                  uploadedFileName={uploadedFileName}
                  onUpload={handleUpload}
                  onRemove={handleRemoveFile}
                  fileNameFromTerms={terms?.file?.fileName}
                />
              </FormCard>

              <FormCard className="conditions-wrapper">
                <ConditionsForm
                  values={triggerPoints}
                  onChange={setTriggerPoints}
                />
              </FormCard>

              <StatementEditor
                value={statementInput}
                onChange={setStatementInput}
              />
            </FormCard>
          </GridLayoutItem>

          <GridLayoutItem>
            <FormCard
              title={t("terms_preview_title")}
              className="content-right"
            >
              <PDFPreviewPanel
                isSaving={isSaving}
                file={selectedFile}
                terms={terms}
                remoteBlobUrl={remoteBlobUrl}
                previewStatement={previewStatement}
                previewBlobUrl={previewBlobUrl}
                initialStatement={defaultPreviewStatement}
              />
            </FormCard>
          </GridLayoutItem>
        </GridLayout>
      </SectionLayout>
    </>
  );
}
