.portal-templates-content {
  padding: 10px 0;
  height: 400px;
}

.template-selection-section {
  margin-bottom: 20px;
}

.template-dropdown-container {
  display: flex;
  align-items: center;
  padding: 0 8px;
  margin-bottom: 16px;
}

.template-dropdown-label {
  font-weight: 500;
  color: #374151;
  min-width: 200px;
  flex-shrink: 0;
}

.template-dropdown-wrapper {
  display: flex;
  flex: 1;
  gap: 12px;
}

.template-dropdown {
  min-width: 250px;
  max-width: 400px;
  flex: 1;
}

.clear-selection-btn {
  margin-left: auto;
}

.folder-structure-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;

  .folder-structure-header {
    background: #f5f5f5;
    padding: 10px 20px;
    border-bottom: 1px solid #e0e0e0;
  }

  .folder-structure-title {
    margin: 0;
  }

  .folder-structure-content {
    color: #6b7280;
    height: 190px;
    overflow-y: auto;
    padding: 20px 24px;
  }

  .no-template-message {
    color: #b91c1c;
    font-weight: 500;
    font-size: 15px;
    text-align: center;
    margin: 24px 0;
  }
}

.clients-footer-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

// TabStrip width fix
.assign-clients-tabstrip {
  width: 100%;

  .k-tabstrip {
    width: 100%;

    .k-tabstrip-items-wrapper {
      width: 100%;
    }

    .k-tabstrip-content {
      width: 100%;
      padding: 0;
    }
  }
}

.contacts-content {
  padding: 20px;
  text-align: center;
  color: #6b7280;
}

.k-tabstrip-content {
  .k-animation-container,
  .k-animation-container-relative {
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
  }
}
