export const TRIGGER_KEYS = {
  PROMPT_AFTER_FIRST_LOGIN: "promptAfterFirstLogin",
  PROMPT_WHEN_UPDATED: "promptWhenUpdated",
  PROMPT_ANNUALLY: "promptAnnually",
  PROMPT_QUARTERLY: "promptQuarterly",
} as const;

export const ALLOWED_FILE_EXTENSIONS = [".pdf"];

export const MAX_UPLOAD_FILE_SIZE_MB = 10;
export const MAX_UPLOAD_FILE_SIZE_BYTES = MAX_UPLOAD_FILE_SIZE_MB * 1024 * 1024;

export const DISPLAY_FREQUENCIES = {
  ANNUALLY: "Annually",
  QUARTERLY: "Quarterly",
} as const;

export type DisplayFrequency =
  (typeof DISPLAY_FREQUENCIES)[keyof typeof DISPLAY_FREQUENCIES];
