import logger from "@/utils/logger";
import { useState, useEffect } from "react";
import {
  useGetTemplateByIdQuery,
  useCreateTemplateMutation,
  useUpdateTemplateMutation,
} from "@/api/templatesApiSlice";
import type {
  CreateTemplateRequest,
  Template,
  CreateTemplateFormData,
  CreateTemplateValidation,
  UpdateTemplateRequest,
} from "@/types/templates";

export function useManageTemplatePopup() {
  const [isOpen, setIsOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | null>(
    null,
  );
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState<CreateTemplateFormData>({
    templateName: "",
    templateDescription: "",
    active: true,
  });
  const [validation, setValidation] = useState<CreateTemplateValidation>({
    nameError: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: templateData, isLoading: isTemplateLoading } =
    useGetTemplateByIdQuery(editingTemplateId!, {
      skip: !isEditMode || !editingTemplateId,
    });

  const [createTemplate] = useCreateTemplateMutation();
  const [updateTemplate] = useUpdateTemplateMutation();

  useEffect(() => {
    if (templateData && isEditMode) {
      setFormData({
        templateName: templateData.templateName || "",
        templateDescription: templateData.templateDescription || "",
        active: templateData.active,
      });
    }
  }, [templateData, isEditMode]);

  const openPopup = () => {
    setIsOpen(true);
    setIsEditMode(false);
    setEditingTemplate(null);
    setFormData({
      templateName: "",
      templateDescription: "",
      active: true,
    });
    setValidation({
      nameError: "",
    });
  };

  const [popupKey, setPopupKey] = useState(0);

  const handleOpenPopup = () => {
    setPopupKey((prev) => prev + 1);
    openPopup();
  };

  const openEditPopup = (templateId: string) => {
    setPopupKey((prev) => prev + 1);
    setIsOpen(true);
    setIsEditMode(true);
    setEditingTemplateId(templateId);
    setEditingTemplate(null);
    setFormData({
      templateName: "",
      templateDescription: "",
      active: true,
    });
    setValidation({
      nameError: "",
    });
  };

  const closePopup = () => {
    setIsOpen(false);
    setIsEditMode(false);
    setEditingTemplateId(null);
    setEditingTemplate(null);
    setIsSubmitting(false);
  };

  const updateField = (
    field: keyof CreateTemplateFormData,
    value: string | boolean,
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));

    if (field === "templateName" && validation.nameError) {
      setValidation((prev) => ({
        ...prev,
        nameError: "",
      }));
    }
  };

  const handleCreate = async (
    payload: CreateTemplateRequest,
    onSuccess?: () => void,
    onError?: () => void,
  ) => {
    setIsSubmitting(true);
    try {
      logger.info("Creating template:", payload);
      const result = await createTemplate(payload).unwrap();
      logger.info("Template created successfully:", result);
      if (result && result.success === true && onSuccess) {
        onSuccess();
      }
    } catch (error) {
      logger.error("Failed to create template:", error as Record<string, any>);
      if (onError) {
        onError();
      }
    } finally {
      setIsSubmitting(false);
      closePopup();
    }
  };

  const handleUpdate = async (
    payload: UpdateTemplateRequest,
    onSuccess?: () => void,
    onError?: () => void,
  ) => {
    setIsSubmitting(true);
    try {
      const result = await updateTemplate(payload).unwrap();
      if (result && result.success === true && onSuccess) {
        onSuccess();
      }
    } catch (error) {
      logger.error("Failed to update template:", error as Record<string, any>);
      if (onError) {
        onError();
      }
    } finally {
      setIsSubmitting(false);
      closePopup();
    }
  };

  const handleCancel = () => {
    closePopup();
  };

  return {
    isOpen,
    isEditMode,
    editingTemplate,
    editingTemplateId,
    formData,
    validation,
    isSubmitting,
    isTemplateLoading,
    openPopup,
    openEditPopup,
    closePopup,
    updateField,
    handleCreate,
    handleUpdate,
    handleCancel,
    handleOpenPopup,
    popupKey,
  };
}
