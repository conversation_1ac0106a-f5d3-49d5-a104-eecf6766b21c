import { useTranslation } from "react-i18next";
import "./Templates.scss";
import SectionLayout from "@/components/dashboardLayout/SectionLayout/SectionLayout";
import { Button } from "@progress/kendo-react-buttons";
import TemplatesGridActions from "./components/TemplatesGridActions";
import TemplateDeleteDialog from "./components/TemplateDelete/TemplateDeleteDialog";
import { useState } from "react";
import AdvancedBaseGrid from "@/components/AdvancedBaseGrid/AdvancedBaseGrid";
import { ManageTemplatePopup, AssignClientsTabStrip } from "./components";
import { useManageTemplatePopup } from "./hooks/useManageTemplatePopup";
import { useClientsAssign } from "./hooks/useClientsAssign";
import "./components/ManageTemplatePopup.scss";
import appConfig from "@/config/app.config";
import BaseGridFilterFactory from "@/components/BaseGridFilterFactory/BaseGridFilterFactory";
import { useTemplatesAutoSuggest } from "./hooks/useTemplatesAutoSuggest";
import { templateGridDataCellMapper } from "./mappers/templateGridDataCellMapper";
import type { GridCustomCellProps } from "@progress/kendo-react-grid";
import { useTemplateList } from "./hooks/useTemplateList";

export default function TemplatesPage() {
  const { t } = useTranslation("dashboard");
  const [openDelete, setOpenDelete] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const handleDeleteDialogClose = () => setOpenDelete(false);

  const {
    activeTab,
    showAlert,
    selectedTemplate,
    templateOptions,
    handleTabSelect,
    handleTemplateChange,
    handleClearSelection,
    handleAlertClose,
    handleFooterCancel,
    handleFooterConfirm,
  } = useClientsAssign();

  const {
    columns,
    isColumnsLoading,
    templateListData,
    isLoading,
    filters,
    pagination,
    sorts,
    handleFilterChange,
    handlePageChange,
    handleRefresh,
    handleSortChange,
    totalRecordCount,
    refetch: refetchTemplates,
    selectedTemplateId,
    setSelectedTemplateId,
    isFetching,
  } = useTemplateList();

  const {
    isOpen,
    isEditMode,
    editingTemplate,
    editingTemplateId,
    formData,
    validation,
    isSubmitting,
    isTemplateLoading,
    openEditPopup,
    updateField,
    handleCreate,
    handleUpdate,
    handleCancel,
    popupKey,
    handleOpenPopup,
  } = useManageTemplatePopup();

  const handleTemplateCreateSuccess = () => {
    setSuccessMessage(t("template_created"));
    refetchTemplates();
  };

  const handleTemplateCreateError = () => {
    setErrorMessage(t("template_create_error"));
  };

  const handleTemplateUpdateSuccess = () => {
    setSuccessMessage(t("template_updated"));
    refetchTemplates();
  };

  const handleTemplateUpdateError = () => {
    setErrorMessage(t("template_update_error"));
  };

  return (
    <SectionLayout
      headerActions={
        <Button
          size="small"
          className="header-action-btn"
          icon="add"
          themeColor="base"
          onClick={handleOpenPopup}
        >
          {t("create_template")}
        </Button>
      }
      successMessage={successMessage}
      onCloseSuccess={() => setSuccessMessage("")}
      errorMessage={errorMessage}
      onCloseError={() => setErrorMessage("")}
      footer={
        <div className="templates-footer-buttons">
          <Button onClick={handleFooterCancel}>{t("cancel")}</Button>
          <Button themeColor="primary" onClick={handleFooterConfirm} disabled={!selectedTemplate}>
            {t("confirm")}
          </Button>
        </div>
      }
    >
      {/* Clients TabStrip */}
      <AssignClientsTabStrip
        activeTab={activeTab}
        showAlert={showAlert}
        selectedTemplate={selectedTemplate}
        templateOptions={templateOptions}
        handleTabSelect={handleTabSelect}
        handleTemplateChange={handleTemplateChange}
        handleClearSelection={handleClearSelection}
        handleAlertClose={handleAlertClose}
      />

      {/* Commented out existing AdvancedBaseGrid - TODO: Remove when moving to portalSettings
      <AdvancedBaseGrid
        totalRecordCount={totalRecordCount}
        columns={columns}
        dataSource={templateListData}
        filters={filters}
        skip={pagination.skip}
        take={pagination.take}
        onFilterChange={handleFilterChange}
        onPageChange={handlePageChange}
        onSortChange={handleSortChange}
        onRefresh={handleRefresh}
        isLoading={isLoading || isFetching}
        sorts={sorts}
        isColumnsLoading={isColumnsLoading}
        actionsColumn={{
          label: t("templates_actions_label"),
          renderer: (props: GridCustomCellProps) => (
            <TemplatesGridActions
              {...props}
              onEdit={() => openEditPopup(props?.dataItem?.templateId)}
              onDelete={() => {
                setSelectedTemplateId(props?.dataItem?.templateId);
                setOpenDelete(true);
              }}
            />
          ),
        }}
        renderFilterFactory={(props, column) => (
          <BaseGridFilterFactory
            {...props}
            column={column}
            useAutoSuggestHook={useTemplatesAutoSuggest}
          />
        )}
        dataCellMapper={templateGridDataCellMapper}
      />
      */}

      {appConfig.featureFlags.ui.CREATE_TEMPLATE && (
        <ManageTemplatePopup
          key={popupKey}
          isOpen={isOpen}
          isEditMode={isEditMode}
          editingTemplate={editingTemplate}
          editingTemplateId={editingTemplateId}
          formData={formData}
          validation={validation}
          isSubmitting={isSubmitting}
          isTemplateLoading={isTemplateLoading}
          onFieldChange={updateField}
          onCreate={(payload) =>
            handleCreate(
              payload,
              handleTemplateCreateSuccess,
              handleTemplateCreateError,
            )
          }
          onUpdate={(payload) =>
            handleUpdate(
              payload,
              handleTemplateUpdateSuccess,
              handleTemplateUpdateError,
            )
          }
          onCancel={handleCancel}
        />
      )}
      <TemplateDeleteDialog
        open={openDelete}
        onClose={handleDeleteDialogClose}
        refetch={refetchTemplates}
        selectedTemplateId={selectedTemplateId}
      />
    </SectionLayout>
  );
}
