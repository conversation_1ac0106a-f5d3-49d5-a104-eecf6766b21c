import config from "@/config";
import apiClient from "@/api/apiClient";
import logger from "@/utils/logger";
import { configStore } from "@/App";

export async function fetchTermsPdfBlob(termsId: number): Promise<Blob> {
  const state = configStore.getState();
  const db = state.okta.userInfo?.tenantCode?.[0];
  const authorization = state.okta?.idToken;

  if (config.featureFlags.api.DOWNLOAD_TERMS_PDF) {
    await new Promise((res) => setTimeout(res, 500));
    const { mockDownloadPdfBlob } = await import("@/api/mocks/terms.mock");
    return mockDownloadPdfBlob;
  }

  try {
    const response = await apiClient.get(
      `${db}/api/terms-and-conditions/${termsId}/download`,
      {
        responseType: "blob",
        headers: {
          Accept: "application/pdf",
          Authorization: "Bearer " + authorization,
        },
      },
    );

    return response.data;
  } catch (error: unknown) {
    const err = error instanceof Error ? error : new Error(String(error));
    logger.error("Failed to download PDF blob", err);
    throw new Error("Unable to download PDF file.");
  }
}
