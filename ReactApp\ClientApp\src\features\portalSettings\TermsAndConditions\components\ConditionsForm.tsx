import { Checkbox, <PERSON>Button } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";
import {
  TRIGGER_KEYS,
  DISPLAY_FREQUENCIES,
  type DisplayFrequency,
} from "../constants";

interface TriggerPoints {
  [TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]: boolean;
  [TRIGGER_KEYS.PROMPT_WHEN_UPDATED]: boolean;
  [TRIGGER_KEYS.PROMPT_ANNUALLY]: boolean;
  [TRIGGER_KEYS.PROMPT_QUARTERLY]: boolean;
}

interface Props {
  values: TriggerPoints;
  onChange: (_updated: TriggerPoints) => void;
}

export default function ConditionsForm({ values, onChange }: Props) {
  const { t } = useTranslation("dashboard");

  const handleCheckboxChange = (key: keyof TriggerPoints) => {
    onChange({ ...values, [key]: !values[key] });
  };

  const handleRadioChange = (value: DisplayFrequency) => {
    onChange({
      ...values,
      [TRIGGER_KEYS.PROMPT_ANNUALLY]: value === DISPLAY_FREQUENCIES.ANNUALLY,
      [TRIGGER_KEYS.PROMPT_QUARTERLY]: value === DISPLAY_FREQUENCIES.QUARTERLY,
    });
  };

  return (
    <div className="conditions-form">
      <div className="conditions-header">
        {t("title_display_conditions")} <span className="required">*</span>
        <div className="conditions-subtext">{t("sub_text")}</div>
      </div>

      <div className="conditions-options">
        {/* Disabled checkbox */}
        <Checkbox
          checked={values[TRIGGER_KEYS.PROMPT_AFTER_FIRST_LOGIN]}
          disabled
          label={<span className="option-label">{t("first_login")}</span>}
        />

        {/* Editable checkbox */}
        <Checkbox
          checked={values[TRIGGER_KEYS.PROMPT_WHEN_UPDATED]}
          onChange={() =>
            handleCheckboxChange(TRIGGER_KEYS.PROMPT_WHEN_UPDATED)
          }
          label={<span className="option-label">{t("updated")}</span>}
        />

        {/* Radio buttons - mutually exclusive */}
        <div className="radio-label">
          <RadioButton
            name="displayFrequency"
            value={DISPLAY_FREQUENCIES.ANNUALLY}
            checked={values[TRIGGER_KEYS.PROMPT_ANNUALLY]}
            onChange={() => handleRadioChange(DISPLAY_FREQUENCIES.ANNUALLY)}
            label=""
          />
          <span>{t("annually")}</span>
        </div>

        <div className="radio-label">
          <RadioButton
            name="displayFrequency"
            value={DISPLAY_FREQUENCIES.QUARTERLY}
            checked={values[TRIGGER_KEYS.PROMPT_QUARTERLY]}
            onChange={() => handleRadioChange(DISPLAY_FREQUENCIES.QUARTERLY)}
            label=""
          />
          <span>{t("quarterly")}</span>
        </div>
      </div>
    </div>
  );
}
