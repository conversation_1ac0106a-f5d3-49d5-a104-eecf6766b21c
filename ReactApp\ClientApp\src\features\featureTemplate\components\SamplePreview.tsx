import { Card } from "@progress/kendo-react-layout";
import { useTranslation } from "react-i18next";

interface Props {
  name: string;
  description: string;
}

export default function SamplePreview({ name, description }: Props) {
  const { t } = useTranslation("dashboard");

  return (
    <Card className="sample-preview">
      <h3>{t("preview_title")}</h3>
      <p>
        <strong>{t("preview_name")}</strong>: {name}
      </p>
      <p>
        <strong>{t("preview_description")}</strong>: {description}
      </p>
    </Card>
  );
}
