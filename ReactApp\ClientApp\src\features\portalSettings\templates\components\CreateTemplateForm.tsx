import { Input, Switch } from "@progress/kendo-react-inputs";
import { useTranslation } from "react-i18next";
import type {
  CreateTemplateFormData,
  CreateTemplateValidation,
} from "@/types/templates";

interface CreateTemplateFormProps {
  formData: CreateTemplateFormData;
  validation: CreateTemplateValidation;
  onFieldChange: (
    _field: keyof CreateTemplateFormData,
    _value: string | boolean,
  ) => void;
}

export default function CreateTemplateForm({
  formData,
  validation,
  onFieldChange,
}: CreateTemplateFormProps) {
  const { t } = useTranslation("dashboard");

  return (
    <div className="create-template-form">
      <div className="form-group">
        <label htmlFor="template-name" className="form-label">
          {t("templates_form_name")}
          <span style={{ color: "red" }}>
            {t("templates_form_name_required")}
          </span>
        </label>
        <Input
          id="template-name"
          name="templateName"
          value={formData.templateName}
          onChange={(e) => onFieldChange("templateName", e.value)}
          placeholder={t("templates_form_name_placeholder")}
          style={{
            width: "100%",
            borderColor: validation.nameError ? "#dc3545" : undefined,
          }}
        />
        {validation.nameError && (
          <div className="validation-error">{validation.nameError}</div>
        )}
      </div>

      <div className="form-group">
        <label htmlFor="template-description" className="form-label">
          {t("templates_form_description")}
        </label>
        <Input
          id="template-description"
          name="templateDescription"
          value={formData.templateDescription}
          onChange={(e) => onFieldChange("templateDescription", e.value)}
          placeholder={t("templates_form_description_placeholder")}
          style={{ width: "100%" }}
        />
      </div>

      <div className="form-group switch-group">
        <div className="switch-container">
          <span className="switch-label">
            {formData.active
              ? t("templates_form_status_active")
              : t("templates_form_status_inactive")}
          </span>
          <Switch
            onLabel=""
            offLabel=""
            checked={formData.active}
            onChange={(e) => onFieldChange("active", e.value)}
          />
        </div>
      </div>
    </div>
  );
}
