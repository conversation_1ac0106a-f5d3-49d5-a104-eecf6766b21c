export const mockClientsColumns = [
  {
    key: "clientRef",
    displayValue: "Client Ref",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "clientName",
    displayValue: "Client Name",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "clientType",
    displayValue: "Client Type",
    dataType: "text",
    defaultVisible: true,
    sortable: true,
    filterType: "search",
  },
  {
    key: "status",
    displayValue: "Status",
    dataType: "text",
    defaultVisible: false,
    sortable: true,
    filterType: "multiSelect",
    filterData: [
      {
        name: "Active",
        value: "Active",
      },
      {
        name: "Inactive",
        value: "Inactive",
      },
    ],
  },
  {
    key: "template",
    displayValue: "Template",
    dataType: "text",
    defaultVisible: false,
    sortable: true,
    filterType: "search",
  },
  {
    key: "partner",
    displayValue: "Partner",
    dataType: "text",
    defaultVisible: false,
    sortable: true,
    filterType: "search",
  },
  {
    key: "manager",
    displayValue: "Manager",
    dataType: "text",
    defaultVisible: false,
    sortable: true,
    filterType: "search",
  },
];

export const mockClientsData = [
  {
    clientRef: 1,
    clientName: "Client A",
    clientType: "Type 1",
    status: "Active",
    template: "Template 1",
    partner: "Partner A",
    manager: "Manager A",
  },
  {
    clientRef: 2,
    clientName: "Client B",
    clientType: "Type 2",
    status: "Inactive",
    template: "Template 2",
    partner: "Partner B",
    manager: "Manager B",
  },
  {
    clientRef: 3,
    clientName: "Client C",
    clientType: "Type 3",
    status: "Active",
    template: "Template 3",
    partner: "Partner C",
    manager: "Manager C",
  },
  {
    clientRef: 4,
    clientName: "Client D",
    clientType: "Type 1",
    status: "Active",
    template: "Template 1",
    partner: "Partner A",
    manager: "Manager D",
  },
  {
    clientRef: 5,
    clientName: "Client E",
    clientType: "Type 2",
    status: "Inactive",
    template: "Template 2",
    partner: "Partner B",
    manager: "Manager A",
  },
  {
    clientRef: 6,
    clientName: "Client F",
    clientType: "Type 3",
    status: "Active",
    template: "Template 3",
    partner: "Partner C",
    manager: "Manager B",
  },
  {
    clientRef: 7,
    clientName: "Client G",
    clientType: "Type 1",
    status: "Inactive",
    template: "Template 4",
    partner: "Partner A",
    manager: "Manager C",
  },
  {
    clientRef: 8,
    clientName: "Client H",
    clientType: "Type 2",
    status: "Active",
    template: "Template 1",
    partner: "Partner B",
    manager: "Manager D",
  },
  {
    clientRef: 9,
    clientName: "Client I",
    clientType: "Type 3",
    status: "Inactive",
    template: "Template 2",
    partner: "Partner C",
    manager: "Manager A",
  },
  {
    clientRef: 10,
    clientName: "Client J",
    clientType: "Type 1",
    status: "Active",
    template: "Template 3",
    partner: "Partner A",
    manager: "Manager B",
  },
  {
    clientRef: 11,
    clientName: "Client K",
    clientType: "Type 2",
    status: "Active",
    template: "Template 1",
    partner: "Partner B",
    manager: "Manager C",
  },
  {
    clientRef: 12,
    clientName: "Client L",
    clientType: "Type 3",
    status: "Inactive",
    template: "Template 2",
    partner: "Partner C",
    manager: "Manager D",
  },
  {
    clientRef: 13,
    clientName: "Client M",
    clientType: "Type 1",
    status: "Active",
    template: "Template 3",
    partner: "Partner A",
    manager: "Manager A",
  },
  {
    clientRef: 14,
    clientName: "Client N",
    clientType: "Type 2",
    status: "Inactive",
    template: "Template 4",
    partner: "Partner B",
    manager: "Manager B",
  },
  {
    clientRef: 15,
    clientName: "Client O",
    clientType: "Type 3",
    status: "Active",
    template: "Template 1",
    partner: "Partner C",
    manager: "Manager C",
  },
  {
    clientRef: 16,
    clientName: "Client P",
    clientType: "Type 1",
    status: "Inactive",
    template: "Template 2",
    partner: "Partner A",
    manager: "Manager D",
  },
  {
    clientRef: 17,
    clientName: "Client Q",
    clientType: "Type 2",
    status: "Active",
    template: "Template 3",
    partner: "Partner B",
    manager: "Manager A",
  },
  {
    clientRef: 18,
    clientName: "Client R",
    clientType: "Type 3",
    status: "Inactive",
    template: "Template 4",
    partner: "Partner C",
    manager: "Manager B",
  },
  {
    clientRef: 19,
    clientName: "Client S",
    clientType: "Type 1",
    status: "Active",
    template: "Template 1",
    partner: "Partner A",
    manager: "Manager C",
  },
  {
    clientRef: 20,
    clientName: "Client T",
    clientType: "Type 2",
    status: "Inactive",
    template: "Template 2",
    partner: "Partner B",
    manager: "Manager D",
  },
  {
    clientRef: 21,
    clientName: "Client U",
    clientType: "Type 3",
    status: "Active",
    template: "Template 3",
    partner: "Partner C",
    manager: "Manager A",
  },
  {
    clientRef: 22,
    clientName: "Client V",
    clientType: "Type 1",
    status: "Active",
    template: "Template 4",
    partner: "Partner A",
    manager: "Manager B",
  },
  {
    clientRef: 23,
    clientName: "Client W",
    clientType: "Type 2",
    status: "Inactive",
    template: "Template 1",
    partner: "Partner B",
    manager: "Manager C",
  },
  {
    clientRef: 24,
    clientName: "Client X",
    clientType: "Type 3",
    status: "Active",
    template: "Template 2",
    partner: "Partner C",
    manager: "Manager D",
  },
  {
    clientRef: 25,
    clientName: "Client Y",
    clientType: "Type 1",
    status: "Inactive",
    template: "Template 3",
    partner: "Partner A",
    manager: "Manager A",
  },
];
