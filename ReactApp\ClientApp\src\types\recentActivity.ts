import type { SortDescriptor } from "@progress/kendo-data-query";

export type SetupAreaType =
  | "DMS Settings"
  | "Portal Settings"
  | "Shared Settings";

export interface RecentActivity {
  id: string;
  setupArea: SetupAreaType;
  section: string;
  updatedBy: string;
  lastUpdated: string;
  relativeUrl: string;
}

export interface RecentActivityResponse {
  records: RecentActivity[];
  pageCount: number;
  pageNumber: number;
  pageSize: number;
  totalRecordCount: number;
}

export interface RecentActivityParams {
  pageNumber?: number;
  pageSize?: number;
  sortField?: string;
  sortDirection?: string;
}

export interface RecentActivityTableProps {
  data: RecentActivity[];
  totalRecords: number;
  dataState: {
    skip: number;
    take: number;
    sort: SortDescriptor[];
  };
  isLoading?: boolean;
  isFetching?: boolean;
  onRefresh?: () => void;
  onDataStateChange?: (_event: any) => void;
}
